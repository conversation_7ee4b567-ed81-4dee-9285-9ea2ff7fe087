'use client';

import React, { useRef, useEffect } from 'react';
import dynamic from 'next/dynamic';
import 'react-quill/dist/quill.snow.css';

// Create a client-side only component
const QuillEditor = ({ value, onChange }: { value: string, onChange: (value: string) => void }) => {
  const editorRef = useRef<HTMLDivElement>(null);
  const quillRef = useRef<any>(null);
  const initializedRef = useRef(false);

  const modules = {
    toolbar: [
      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      ['link'],
      ['clean']
    ],
  };

  // Initialize Quill on component mount
  useEffect(() => {
    // Only initialize once
    if (initializedRef.current) return;

    if (editorRef.current) {
      try {
        // Dynamically import Quill
        const Quill = require('quill');

        // Remove any existing toolbar to prevent duplicates
        const existingToolbar = editorRef.current.parentElement?.querySelector('.ql-toolbar');
        if (existingToolbar) {
          existingToolbar.remove();
        }

        // Create a new Quill instance
        quillRef.current = new Quill(editorRef.current, {
          theme: 'snow',
          modules: modules,
          placeholder: 'Write something...',
        });

        // Set initial value
        if (value) {
          quillRef.current.clipboard.dangerouslyPasteHTML(value);
        }

        // Handle changes
        quillRef.current.on('text-change', () => {
          const html = editorRef.current?.querySelector('.ql-editor')?.innerHTML || '';
          onChange(html);
        });

        initializedRef.current = true;
      } catch (error) {
        console.error('Error initializing Quill:', error);
      }
    }

    // Cleanup on unmount
    return () => {
      if (quillRef.current) {
        quillRef.current = null;
      }
      initializedRef.current = false;
    };
  }, []);

  // Update content when value prop changes
  useEffect(() => {
    if (quillRef.current && initializedRef.current) {
      const currentContent = quillRef.current.root.innerHTML;
      if (value !== currentContent) {
        quillRef.current.clipboard.dangerouslyPasteHTML(value || '');
      }
    }
  }, [value]);

  return (
    <div className="react-quill-container min-h-[200px]">
      <div ref={editorRef} className="min-h-[200px]"></div>
    </div>
  );
};

// Dynamically import the editor to avoid SSR issues
const DynamicQuillEditor = dynamic(
  () => Promise.resolve(QuillEditor),
  { ssr: false }
);

interface ReactQuillEditorProps {
  value: string;
  onChange: (value: string) => void;
}

const ReactQuillEditor: React.FC<ReactQuillEditorProps> = ({ value, onChange }) => {
  return (
    <div className="react-quill-wrapper">
      <DynamicQuillEditor value={value} onChange={onChange} />
      <style jsx global>{`
        .react-quill-container {
          min-height: 200px;
        }
        .react-quill-container .ql-container {
          min-height: 150px;
          border-bottom-left-radius: 0.375rem;
          border-bottom-right-radius: 0.375rem;
        }
        .react-quill-container .ql-toolbar {
          border-top-left-radius: 0.375rem;
          border-top-right-radius: 0.375rem;
        }
        .react-quill-container .ql-editor {
          min-height: 150px;
        }
      `}</style>
    </div>
  );
};

export default ReactQuillEditor;
