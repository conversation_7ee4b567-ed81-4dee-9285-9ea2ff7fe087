'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useRouter } from 'next/navigation';
import { signIn } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import Link from 'next/link';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle
} from '@/components/ui/card';

const formSchema = z.object({
	email: z.string().email({
		message: 'Please enter a valid email address.'
	}),
	password: z.string().min(6, {
		message: 'Password must be at least 6 characters.'
	})
});

export default function LoginPage() {
	const { toast } = useToast();
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(false);

	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			email: '',
			password: ''
		}
	});

	async function onSubmit(values: z.infer<typeof formSchema>) {
		setIsLoading(true);
		try {
			const result = await signIn('credentials', {
				redirect: false,
				email: values.email,
				password: values.password
			});

			if (result?.error) {
				toast({
					title: 'Error',
					description: result.error,
					type: 'error'
				});
			} else {
				toast({
					title: 'Success',
					description: 'Logged in successfully.',
					type: 'success'
				});
				router.push('/dashboard');
			}
		} catch (error) {
			console.error('Login error:', error);
			toast({
				title: 'Error',
				description: 'An unexpected error occurred',
				type: 'error'
			});
		} finally {
			setIsLoading(false);
		}
	}

	return (
		<div className="container mx-auto px-4 py-8 flex justify-center items-center min-h-screen">
			<Card className="w-full max-w-md">
				<CardHeader>
					<CardTitle>Login</CardTitle>
					<CardDescription>
						Enter your credentials to access your account
					</CardDescription>
				</CardHeader>
				<CardContent>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel htmlFor="email">Email</FormLabel>
										<FormControl>
											<Input
												id="email"
												placeholder="Your email"
												{...field}
												autoComplete="email"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="password"
								render={({ field }) => (
									<FormItem>
										<FormLabel htmlFor="password">Password</FormLabel>
										<FormControl>
											<Input
												id="password"
												type="password"
												placeholder="Your password"
												{...field}
												autoComplete="current-password"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<Button type="submit" className="w-full" disabled={isLoading}>
								{isLoading ? 'Logging in...' : 'Login'}
							</Button>
						</form>
					</Form>
				</CardContent>
				<CardFooter className="flex justify-between">
					<Link
						href="/forgot-password"
						className="text-sm text-blue-500 hover:underline"
					>
						Forgot password?
					</Link>
					<Link
						href="/register"
						className="text-sm text-blue-500 hover:underline"
					>
						Don&apos;t have an account? Register
					</Link>
				</CardFooter>
			</Card>
		</div>
	);
}
