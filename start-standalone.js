const { join } = require('path');
const { createServer } = require('http');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');

// Create the standalone server
const app = next({
  dir: join(__dirname, '.next/standalone'),
  dev: false,
  hostname: '0.0.0.0',
  port: 3000,
});

const handle = app.getRequestHandler();

app.prepare().then(() => {
  createServer((req, res) => {
    const parsedUrl = parse(req.url, true);
    const { pathname } = parsedUrl;

    // Serve static files from the .next/static directory
    if (pathname.startsWith('/_next/static/')) {
      const staticFilePath = join(__dirname, '.next/static', pathname.replace('/_next/static/', ''));
      if (fs.existsSync(staticFilePath)) {
        const stream = fs.createReadStream(staticFilePath);
        stream.pipe(res);
        return;
      }
    }

    // Let Next.js handle all other requests
    handle(req, res, parsedUrl);
  }).listen(3000, (err) => {
    if (err) throw err;
    console.log('> Ready on http://localhost:3000');
  });
});
