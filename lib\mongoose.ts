import mongoose from 'mongoose';

const MONGODB_URI = process.env.MONGODB_URI;

// MONGODB_URI is defined in .env

if (!MONGODB_URI) {
	throw new Error(
		'Please define the MONGODB_URI environment variable inside .env'
	);
}

// Define the shape of the cached mongoose connection
interface CachedMongoose {
	conn: typeof mongoose | null;
	promise: Promise<typeof mongoose> | null;
}

// Declare global namespace to add mongoose property to NodeJS.Global
declare global {
	var mongoose: CachedMongoose | undefined;
}

let cached: CachedMongoose = global.mongoose || { conn: null, promise: null };

if (!global.mongoose) {
	global.mongoose = cached;
}

export async function connectToDatabase(): Promise<typeof mongoose> {
	if (cached.conn) {
		return cached.conn;
	}

	if (!cached.promise) {
		const opts = {
			bufferCommands: false,
			serverSelectionTimeoutMS: 30000, // Increase timeout to 30 seconds
			socketTimeoutMS: 45000 // Add socket timeout
		};

		cached.promise = mongoose
			.connect(MONGODB_URI!, opts)
			.then((mongoose) => {
				return mongoose;
			})
			.catch((error) => {
				throw error;
			});
	}

	try {
		cached.conn = await cached.promise;
	} catch (e) {
		cached.promise = null;
		throw e;
	}

	return cached.conn;
}

// New function to handle database operations
export async function withDatabase<T>(operation: () => Promise<T>): Promise<T> {
	try {
		await connectToDatabase();
		const result = await operation();
		return result;
	} catch (error) {
		throw error;
	}
}
