/**
 * Site-wide configuration settings
 * 
 * This file centralizes all configuration settings for the site,
 * making it easier to update links and other settings in one place.
 */

export const siteConfig = {
  name: "RealSoft Games",
  description: "Game Development Portfolio",
  url: process.env.NEXT_PUBLIC_URL || "https://realsoftgames.com",
  
  // Social media links
  social: {
    discord: "https://discord.com/invite/AUrh5Xd",
    facebook: "https://www.facebook.com/profile.php?id=100063705982420",
    linkedin: "https://www.linkedin.com/in/jake-aquilina-632095101/",
    youtube: "https://www.youtube.com/channel/UCaARwTTkFioNNJ3jjhRX1WQ",
    unityAssetStore: "https://assetstore.unity.com/publishers/12871",
  },
  
  // Contact information
  contact: {
    email: process.env.GMAIL_USER || "<EMAIL>",
  },
  
  // Legal pages
  legal: {
    privacyPolicy: "/privacy-policy",
    termsOfService: "/terms-of-service",
  },
  
  // MinIO configuration for client-side use
  minio: {
    publicUrl: process.env.MINIO_PUBLIC_URL || "https://minioapi.realsoftgames.com",
    bucket: process.env.MINIO_BUCKET || "realsoftgames",
  }
};

export default siteConfig;
