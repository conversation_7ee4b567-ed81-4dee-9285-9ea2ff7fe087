'use client';

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeft, Copy } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

export default function ApiDocsPage() {
  const { toast } = useToast();

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied',
      description: 'Code snippet copied to clipboard',
    });
  };

  return (
    <div className="container mx-auto py-10 space-y-6">
      <div className="flex items-center gap-4">
        <Link href="/">
          <Button variant="outline" size="icon">
            <ArrowLeft className="h-4 w-4" />
          </Button>
        </Link>
        <h1 className="text-3xl font-bold">API Documentation</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Getting Started</CardTitle>
          <CardDescription>
            Learn how to use the RealSoft Games API to access products and projects programmatically.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <p>
            The RealSoft Games API allows you to access our products and projects data programmatically.
            To use the API, you need an API token which you can generate in the admin dashboard.
          </p>

          <div className="space-y-2">
            <h3 className="text-lg font-medium">Authentication</h3>
            <p className="text-sm text-muted-foreground">
              All API requests require authentication using an API token. Include your token in the Authorization header:
            </p>
            <div className="relative">
              <pre className="bg-muted p-4 rounded-md overflow-x-auto">
                <code>Authorization: Bearer YOUR_API_TOKEN</code>
              </pre>
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2"
                onClick={() => copyToClipboard('Authorization: Bearer YOUR_API_TOKEN')}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-medium">Base URL</h3>
            <p className="text-sm text-muted-foreground">
              All API endpoints are relative to:
            </p>
            <div className="relative">
              <pre className="bg-muted p-4 rounded-md overflow-x-auto">
                <code>https://realsoftgames.com/api/public</code>
              </pre>
              <Button
                variant="ghost"
                size="icon"
                className="absolute top-2 right-2"
                onClick={() => copyToClipboard('https://realsoftgames.com/api/public')}
              >
                <Copy className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="products">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="products">Products</TabsTrigger>
          <TabsTrigger value="examples">Code Examples</TabsTrigger>
        </TabsList>

        <TabsContent value="products" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Get All Products</CardTitle>
              <CardDescription>
                Retrieve a list of all published products.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <span className="bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-100 px-2 py-1 rounded text-sm font-mono">GET</span>
                  <code className="font-mono">/products</code>
                </div>

                <h4 className="font-medium mt-4">Query Parameters</h4>
                <div className="border rounded-md">
                  <table className="min-w-full divide-y divide-border">
                    <thead>
                      <tr>
                        <th className="px-4 py-2 text-left text-sm font-medium">Parameter</th>
                        <th className="px-4 py-2 text-left text-sm font-medium">Type</th>
                        <th className="px-4 py-2 text-left text-sm font-medium">Description</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border">
                      <tr>
                        <td className="px-4 py-2 text-sm font-mono">featured</td>
                        <td className="px-4 py-2 text-sm">Boolean</td>
                        <td className="px-4 py-2 text-sm">Filter to only featured products</td>
                      </tr>
                      <tr>
                        <td className="px-4 py-2 text-sm font-mono">status</td>
                        <td className="px-4 py-2 text-sm">String</td>
                        <td className="px-4 py-2 text-sm">Filter by status (live, draft, archived)</td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                <h4 className="font-medium mt-4">Example Response</h4>
                <div className="relative">
                  <pre className="bg-muted p-4 rounded-md overflow-x-auto">
                    <code>{JSON.stringify([
                      {
                        "_id": "60d21b4667d0d8992e610c85",
                        "title": "Unity Asset: Advanced Character Controller",
                        "description": "A professional character controller for Unity games with advanced movement features.",
                        "price": "$49.99",
                        "media": [
                          {
                            "_id": "60d21b4667d0d8992e610c86",
                            "type": "image",
                            "url": "https://minioapi.realsoftgames.com/realsoftgames/products/character-controller.jpg",
                            "alt": "Character Controller Preview"
                          }
                        ],
                        "externalLink": "https://assetstore.unity.com/packages/slug/12345",
                        "liveDemoLink": "https://demo.realsoftgames.com/character-controller",
                        "status": "live",
                        "userId": "60d21b4667d0d8992e610c84",
                        "isFeatured": true,
                        "order": 1,
                        "createdAt": "2023-01-15T12:00:00.000Z",
                        "updatedAt": "2023-01-15T12:00:00.000Z"
                      }
                    ], null, 2)}</code>
                  </pre>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2"
                    onClick={() => copyToClipboard(JSON.stringify([
                      {
                        "_id": "60d21b4667d0d8992e610c85",
                        "title": "Unity Asset: Advanced Character Controller",
                        "description": "A professional character controller for Unity games with advanced movement features.",
                        "price": "$49.99",
                        "media": [
                          {
                            "_id": "60d21b4667d0d8992e610c86",
                            "type": "image",
                            "url": "https://minioapi.realsoftgames.com/realsoftgames/products/character-controller.jpg",
                            "alt": "Character Controller Preview"
                          }
                        ],
                        "externalLink": "https://assetstore.unity.com/packages/slug/12345",
                        "liveDemoLink": "https://demo.realsoftgames.com/character-controller",
                        "status": "live",
                        "userId": "60d21b4667d0d8992e610c84",
                        "isFeatured": true,
                        "order": 1,
                        "createdAt": "2023-01-15T12:00:00.000Z",
                        "updatedAt": "2023-01-15T12:00:00.000Z"
                      }
                    ], null, 2))}
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="examples" className="space-y-4 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>JavaScript / Node.js</CardTitle>
              <CardDescription>
                Example of how to use the API with JavaScript.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative">
                <pre className="bg-muted p-4 rounded-md overflow-x-auto">
                  <code>{`// Fetch all products
const fetchProducts = async () => {
  const response = await fetch('https://realsoftgames.com/api/public/products', {
    headers: {
      'Authorization': 'Bearer YOUR_API_TOKEN'
    }
  });

  if (!response.ok) {
    throw new Error('API request failed');
  }

  const data = await response.json();
  return data;
};

// Fetch featured products
const fetchFeaturedProducts = async () => {
  const response = await fetch('https://realsoftgames.com/api/public/products?featured=true', {
    headers: {
      'Authorization': 'Bearer YOUR_API_TOKEN'
    }
  });

  if (!response.ok) {
    throw new Error('API request failed');
  }

  const data = await response.json();
  return data;
};`}</code>
                </pre>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2"
                  onClick={() => copyToClipboard(`// Fetch all products
const fetchProducts = async () => {
  const response = await fetch('https://realsoftgames.com/api/public/products', {
    headers: {
      'Authorization': 'Bearer YOUR_API_TOKEN'
    }
  });

  if (!response.ok) {
    throw new Error('API request failed');
  }

  const data = await response.json();
  return data;
};

// Fetch featured products
const fetchFeaturedProducts = async () => {
  const response = await fetch('https://realsoftgames.com/api/public/products?featured=true', {
    headers: {
      'Authorization': 'Bearer YOUR_API_TOKEN'
    }
  });

  if (!response.ok) {
    throw new Error('API request failed');
  }

  const data = await response.json();
  return data;
};`)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Python</CardTitle>
              <CardDescription>
                Example of how to use the API with Python.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative">
                <pre className="bg-muted p-4 rounded-md overflow-x-auto">
                  <code>{`import requests

# API configuration
API_TOKEN = "YOUR_API_TOKEN"
BASE_URL = "https://realsoftgames.com/api/public"

# Headers for authentication
headers = {
    "Authorization": f"Bearer {API_TOKEN}"
}

# Fetch all products
def fetch_products():
    response = requests.get(f"{BASE_URL}/products", headers=headers)
    response.raise_for_status()  # Raise exception for 4XX/5XX responses
    return response.json()

# Fetch featured products
def fetch_featured_products():
    response = requests.get(f"{BASE_URL}/products?featured=true", headers=headers)
    response.raise_for_status()
    return response.json()

# Example usage
try:
    products = fetch_products()
    print(f"Found {len(products)} products")

    featured_products = fetch_featured_products()
    print(f"Found {len(featured_products)} featured products")
except requests.exceptions.RequestException as e:
    print(f"API request failed: {e}")
`}</code>
                </pre>
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2"
                  onClick={() => copyToClipboard(`import requests

# API configuration
API_TOKEN = "YOUR_API_TOKEN"
BASE_URL = "https://realsoftgames.com/api/public"

# Headers for authentication
headers = {
    "Authorization": f"Bearer {API_TOKEN}"
}

# Fetch all products
def fetch_products():
    response = requests.get(f"{BASE_URL}/products", headers=headers)
    response.raise_for_status()  # Raise exception for 4XX/5XX responses
    return response.json()

# Fetch featured products
def fetch_featured_products():
    response = requests.get(f"{BASE_URL}/products?featured=true", headers=headers)
    response.raise_for_status()
    return response.json()

# Example usage
try:
    products = fetch_products()
    print(f"Found {len(products)} products")

    featured_products = fetch_featured_products()
    print(f"Found {len(featured_products)} featured products")
except requests.exceptions.RequestException as e:
    print(f"API request failed: {e}")
`)}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
          <CardDescription>
            If you need assistance with the API, please contact us.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p>
            For any questions or issues with the API, please contact us through our{' '}
            <Link href="/contact" className="text-primary hover:underline">
              contact form
            </Link>
            {' '}or email us at <a href="mailto:<EMAIL>" className="text-primary hover:underline">
              <EMAIL>
            </a>.
          </p>
          <p className="mt-2">
            If you're an admin, you can manage your API tokens in the{' '}
            <Link href="/dashboard/api-tokens" className="text-primary hover:underline">
              dashboard
            </Link>.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
