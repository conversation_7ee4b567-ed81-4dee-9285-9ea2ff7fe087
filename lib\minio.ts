// Import the AWS S3 SDK which is more compatible with Next.js
import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand, ListObjectsV2Command, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

// Construct the endpoint URL correctly
const useSSL = process.env.MINIO_USE_SSL === 'true';
const endpoint = process.env.MINIO_ENDPOINT || '';
const endpointUrl = `http${useSSL ? 's' : ''}://${endpoint}`;

// Log the endpoint URL for debugging
console.log(`MinIO endpoint URL: ${endpointUrl}`);

// Public URL for client-side access - prioritize the public URL
const publicUrl = process.env.MINIO_PUBLIC_URL || endpointUrl;

// Cache the bucket name to avoid repeated environment variable lookups
const bucket = process.env.MINIO_BUCKET || 'realsoftgames';

// Log MinIO configuration for debugging
console.log(`MinIO configuration:
  Endpoint: ${endpointUrl}
  Public URL: ${publicUrl}
  Bucket: ${bucket}
  Region: ${process.env.MINIO_REGION || 'us-east-1'}
  Access Key: ${process.env.MINIO_ACCESS_KEY ? '***' + process.env.MINIO_ACCESS_KEY.substring(process.env.MINIO_ACCESS_KEY.length - 4) : 'not set'}
  Secret Key: ${process.env.MINIO_SECRET_KEY ? '***' : 'not set'}
`);

// Create a more Next.js-friendly S3 client for MinIO
const s3Client = new S3Client({
  region: process.env.MINIO_REGION || 'us-east-1',
  endpoint: endpointUrl,
  credentials: {
    accessKeyId: process.env.MINIO_ACCESS_KEY || '',
    secretAccessKey: process.env.MINIO_SECRET_KEY || '',
  },
  forcePathStyle: true, // Required for MinIO
});

/**
 * Upload a file to MinIO
 * @param file The file buffer to upload
 * @param key The object key (path) in the bucket
 * @param contentType The content type of the file
 * @returns The URL of the uploaded file
 */
export async function uploadFile(file: Buffer, key: string, contentType: string): Promise<string> {
  try {
    console.log(`MinIO upload: bucket=${bucket}, key=${key}, contentType=${contentType}, size=${file.length} bytes`);

    const params = {
      Bucket: bucket,
      Key: key,
      Body: file,
      ContentType: contentType,
    };

    // Use the S3 client to put the object with improved error handling
    const command = new PutObjectCommand(params);
    console.log(`Sending PutObjectCommand to MinIO endpoint: ${endpointUrl}`);

    const result = await s3Client.send(command);
    console.log(`MinIO upload successful: ${JSON.stringify(result.$metadata)}`);

    // Return the direct MinIO URL using the public URL from environment
    const url = `${publicUrl}/${bucket}/${key}`;
    console.log(`Generated MinIO URL: ${url}`);
    return url;
  } catch (error) {
    console.error('Error uploading file to MinIO:', error);
    // Provide more detailed error information
    const errorDetails = error instanceof Error
      ? `${error.name}: ${error.message}`
      : JSON.stringify(error);

    throw new Error(`Failed to upload file to storage: ${errorDetails}`);
  }
}

/**
 * Delete a file from MinIO
 * @param bucketName The bucket name (optional, defaults to the configured bucket)
 * @param key The object key (path) in the bucket, or a full URL
 */
export async function deleteFile(bucketName: string | undefined, key: string): Promise<void> {
  try {
    // Use the default bucket if not provided
    const targetBucket = bucketName || bucket;

    // Extract the key from the URL if it's a full URL
    if (key.startsWith('http')) {
      const urlObj = new URL(key);
      const pathParts = urlObj.pathname.split('/');

      // If the URL contains a bucket name, use it
      if (pathParts.length >= 2) {
        // The first part is empty, the second is the bucket name
        const urlBucket = pathParts[1];
        const urlKey = pathParts.slice(2).join('/');

        // Only override the bucket if it wasn't explicitly provided
        if (!bucketName) {
          bucketName = urlBucket;
        }

        key = urlKey;
      }
    }

    const params = {
      Bucket: targetBucket,
      Key: key,
    };

    // Use the S3 client to delete the object with improved error handling
    const command = new DeleteObjectCommand(params);
    await s3Client.send(command);
    console.log(`Successfully deleted file: ${targetBucket}/${key}`);
  } catch (error) {
    console.error('Error deleting file from MinIO:', error);
    throw new Error(`Failed to delete file from storage: ${(error as Error).message}`);
  }
}

/**
 * Generate a presigned URL for a file in MinIO
 * @param key The object key (path) in the bucket
 * @param expiresIn The expiration time in seconds (default: 3600)
 * @returns The presigned URL
 */
export async function getPresignedUrl(key: string, expiresIn: number = 3600): Promise<string> {
  try {
    const params = {
      Bucket: bucket,
      Key: key,
    };

    // Use the S3 client to generate a presigned URL with improved error handling
    const command = new GetObjectCommand(params);
    const url = await getSignedUrl(s3Client, command, { expiresIn });

    return url;
  } catch (error) {
    console.error('Error generating presigned URL:', error);
    throw new Error(`Failed to generate presigned URL: ${(error as Error).message}`);
  }
}

/**
 * Get the public URL for a file in MinIO
 * @param key The object key (path) in the bucket
 * @returns The public URL
 */
export function getPublicUrl(key: string): string {
  // Use the publicUrl variable we defined at the top of the file
  return `${publicUrl}/${bucket}/${key}`;
}

/**
 * Check if an object exists in MinIO
 * @param key The object key (path) in the bucket
 * @returns True if the object exists, false otherwise
 */
export async function objectExists(key: string): Promise<boolean> {
  try {
    const params = {
      Bucket: bucket,
      Key: key,
    };

    // Use the S3 client to check if the object exists
    const command = new HeadObjectCommand(params);
    await s3Client.send(command);
    return true;
  } catch (error) {
    // If the error is because the object doesn't exist, return false
    // Otherwise, log the error and return false
    if ((error as any).name === 'NotFound' || (error as any).$metadata?.httpStatusCode === 404) {
      return false;
    }
    console.error('Error checking if object exists:', error);
    return false;
  }
}

/**
 * List objects in a bucket with a prefix
 * @param prefix The prefix to filter objects by
 * @param maxKeys Maximum number of keys to return (default: 1000)
 * @returns An array of object information
 */
export async function listObjects(prefix: string = '', maxKeys: number = 1000): Promise<{ Key: string, Size: number, LastModified: Date }[]> {
  try {
    const params = {
      Bucket: bucket,
      Prefix: prefix,
      MaxKeys: maxKeys,
    };

    // Use the S3 client to list objects
    const command = new ListObjectsV2Command(params);
    const response = await s3Client.send(command);

    // Transform the response to a simpler format
    const objects = (response.Contents || []).map(item => ({
      Key: item.Key || '',
      Size: item.Size || 0,
      LastModified: item.LastModified || new Date(),
    }));

    return objects;
  } catch (error) {
    console.error('Error listing objects:', error);
    throw new Error(`Failed to list objects from storage: ${(error as Error).message}`);
  }
}

/**
 * Convert a MinIO URL to a proxy URL
 * @param url The MinIO URL
 * @returns The proxy URL
 */
export function convertToProxyUrl(url: string): string {
  if (!url) return url;

  // Check if it's already a proxy URL
  if (url.startsWith('/minio/')) {
    return url;
  }

  // Check if it's a MinIO URL
  if (url.includes('minioapi.realsoftgames.com') || url.includes('192.168.0.59:9000')) {
    // Extract the path after the domain
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');

    // The first part is empty, the second is the bucket name
    if (pathParts.length >= 3) {
      const bucket = pathParts[1];
      const key = pathParts.slice(2).join('/');
      return `/minio/${bucket}/${key}`;
    }
  }

  // Return the original URL if it's not a MinIO URL
  return url;
}

/**
 * Convert a proxy URL to a direct MinIO URL
 * @param url The proxy URL
 * @returns The direct MinIO URL
 */
export function convertToDirectUrl(url: string): string {
  if (!url) return url;

  // Fast path: If it's already a direct MinIO URL, return it as is
  if (url.includes('minioapi.realsoftgames.com') || url.includes('192.168.0.59')) {
    return url;
  }

  // If it's a /minio/ URL, convert it to the direct MinIO URL
  if (url.startsWith('/minio/')) {
    const pathParts = url.split('/');

    if (pathParts.length >= 3) {
      const bucket = pathParts[2];
      const key = pathParts.slice(3).join('/');
      // Use the cached publicUrl for better performance
      return `${publicUrl}/${bucket}/${key}`;
    }
  }

  // If it's a proxy URL, extract the original URL
  if (url.startsWith('/api/')) {
    try {
      // Extract the URL parameter from any proxy endpoint
      const urlObj = new URL(url, 'http://localhost');
      const originalUrl = urlObj.searchParams.get('url');
      if (originalUrl) {
        // Decode and return the original URL
        return decodeURIComponent(originalUrl);
      }
    } catch (error) {
      // If there's an error parsing the URL, just continue
    }
  }

  // If it's not a MinIO URL or we couldn't extract it, return unchanged
  return url;
}

/**
 * Test the connection to MinIO
 * @returns A promise that resolves to true if the connection is successful, false otherwise
 */
export async function testMinioConnection(): Promise<boolean> {
  try {
    console.log('Testing MinIO connection...');

    // Try to list objects in the bucket (with a limit of 1)
    const params = {
      Bucket: bucket,
      MaxKeys: 1,
    };

    const command = new ListObjectsV2Command(params);
    const response = await s3Client.send(command);

    console.log('MinIO connection test successful:', response.$metadata);
    return true;
  } catch (error) {
    console.error('MinIO connection test failed:', error);
    return false;
  }
}
