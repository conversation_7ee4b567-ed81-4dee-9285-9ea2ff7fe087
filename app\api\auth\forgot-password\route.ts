import { NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongoose'
import User from '@/models/User'
import crypto from 'crypto'
import nodemailer from 'nodemailer'

export async function POST(request: Request) {
  try {
    const { email } = await request.json()
    await connectToDatabase()

    const user = await User.findOne({ email })

    if (!user) {
      // We don't want to reveal if the email exists or not
      return NextResponse.json({ message: 'If an account with that email exists, we have sent a password reset link.' })
    }

    const resetToken = crypto.randomBytes(20).toString('hex')
    const resetTokenExpiry = Date.now() + 3600000 // 1 hour from now

    user.resetToken = resetToken
    user.resetTokenExpiry = resetTokenExpiry
    await user.save()

    const transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: process.env.GMAIL_USER,
        pass: process.env.GMAIL_APP_PASSWORD,
      },
    })

    const resetUrl = `${process.env.NEXTAUTH_URL}/reset-password?token=${resetToken}`

    await transporter.sendMail({
      from: process.env.GMAIL_USER,
      to: user.email,
      subject: 'Password Reset Request',
      html: `
        <p>You requested a password reset. Click the link below to reset your password:</p>
        <a href="${resetUrl}">${resetUrl}</a>
        <p>If you didn't request this, please ignore this email.</p>
      `,
    })

    return NextResponse.json({ message: 'If an account with that email exists, we have sent a password reset link.' })
  } catch (error) {
    console.error('Forgot password error:', error)
    return NextResponse.json({ message: 'An error occurred while processing your request' }, { status: 500 })
  }
}