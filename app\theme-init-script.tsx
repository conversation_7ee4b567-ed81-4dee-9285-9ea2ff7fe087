// This script will be injected into the HTML head to prevent dark mode flickering
export function ThemeInitScript() {
  const script = `
    (function() {
      // Get the theme from localStorage or default to dark
      var theme = localStorage.getItem('theme') || 'dark';
      
      // Apply the theme class to the HTML element
      document.documentElement.classList.toggle('dark', theme === 'dark');
      
      // Set the color-scheme CSS property
      document.documentElement.style.colorScheme = theme;
    })();
  `;

  return <script dangerouslySetInnerHTML={{ __html: script }} />;
}
