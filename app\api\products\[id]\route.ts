import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongoose';
import Product from '@/models/Product';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import path from 'path';
import fs from 'fs';
import { serializeData } from '@/lib/serialization';
import { cookies, headers } from 'next/headers';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Explicitly await cookies and headers to avoid warnings
    const cookiesList = await cookies();
    const headersList = await headers();

    await connectToDatabase();

    const { id } = await params;
    const product = await Product.findById(id).lean();

    if (!product) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    // Use our serialization utility to handle all MongoDB objects
    const serializedProduct = serializeData(product);

    return NextResponse.json(serializedProduct);
  } catch (error) {
    console.error('Error fetching product:', error);
    return NextResponse.json(
      { error: 'Failed to fetch product' },
      { status: 500 }
    );
  }
}

export async function PATCH(
	req: Request,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		await connectToDatabase();

		// Explicitly await cookies and headers to avoid warnings
		const cookiesList = await cookies();
		const headersList = await headers();

		// Await authOptions to properly handle cookies and headers
		const options = await authOptions();
		const session = await getServerSession(options);
		if (!session || !session.user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { id } = await params;
		const data = await req.json();

		const product = await Product.findByIdAndUpdate(
			id,
			{
				...data,
				liveDemoLink: data.liveDemoLink
			},
			{ new: true, runValidators: true }
		);

		if (!product) {
			return NextResponse.json({ error: 'Product not found' }, { status: 404 });
		}

		// Serialize the product to avoid MongoDB object issues
		const serializedProduct = serializeData(product.toObject());
		return NextResponse.json(serializedProduct);
	} catch (error) {
		console.error('Error updating product:', error);
		return NextResponse.json(
			{
				error: 'Failed to update product',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
}

export async function DELETE(
	req: Request,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		await connectToDatabase();

		// Explicitly await cookies and headers to avoid warnings
		const cookiesList = await cookies();
		const headersList = await headers();

		// Await authOptions to properly handle cookies and headers
		const options = await authOptions();
		const session = await getServerSession(options);
		if (!session || !session.user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { id } = await params;

		const product = await Product.findById(id);

		if (!product) {
			return NextResponse.json({ error: 'Product not found' }, { status: 404 });
		}

		// Delete associated media files
		for (const mediaItem of product.media) {
			if (mediaItem.type === 'image' && mediaItem.url.startsWith('/uploads/')) {
				const filePath = path.join(process.cwd(), 'public', mediaItem.url);
				if (fs.existsSync(filePath)) {
					fs.unlinkSync(filePath);
				}
			}
		}

		// Delete the product from the database
		await Product.findByIdAndDelete(id);

		return NextResponse.json({
			message: 'Product and associated media deleted successfully'
		});
	} catch (error) {
		console.error('Error deleting product:', error);
		return NextResponse.json(
			{
				error: 'Failed to delete product',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
}
