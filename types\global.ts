export enum UserRole {
	USER = 'user',
	ADMIN = 'admin'
}

export enum Status {
	Draft = 'Draft',
	Published = 'Published',
	Archived = 'Archived'
}

export enum StatusFilter {
	All = 'All',
	Draft = 'Draft',
	Published = 'Published',
	Archived = 'Archived'
}

export interface User {
	id: string;
	username: string;
	name: string;
	email: string;
	role: UserRole;
	dateOfBirth: string;
	country?: string;
	avatar?: string;
}

export interface ApiToken {
	_id: string;
	userId: string;
	name: string;
	token?: string;
	lastUsed?: string;
	usageCount?: number;
	createdAt: string;
	updatedAt: string;
}
