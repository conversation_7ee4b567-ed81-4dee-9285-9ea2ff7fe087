import mongoose, { Schema, Document } from 'mongoose';
import { Status } from '@/types/global';
import MediaSchema, { IMedia } from './Media';

export interface IProduct extends Document {
	_id: mongoose.Types.ObjectId;
	title: string;
	description: string;
	price: string;
	media: IMedia[];
	externalLink?: string;
	liveDemoLink?: string;
	status: Status;
	userId: string;
	isFeatured: boolean;
	order: number;
	webglDemo?: {
		enabled: boolean;
		demoUrl?: string;
		mediaId?: string; // Reference to the WebGL media item
	};
	createdAt: Date;
	updatedAt: Date;
}

const ProductSchema: Schema = new Schema(
	{
		title: { type: String, required: true },
		description: { type: String, required: true },
		price: { type: String, required: true },
		media: [MediaSchema],
		externalLink: { type: String, default: '' },
		liveDemoLink: { type: String, default: '' },
		status: {
			type: String,
			enum: Object.values(Status),
			default: Status.Draft
		},
		userId: { type: String, required: true },
		isFeatured: { type: Boolean, default: false },
		order: { type: Number, default: 0 },
		webglDemo: {
			enabled: { type: Boolean, default: false },
			demoUrl: { type: String, default: '' },
			mediaId: { type: String, default: '' }
		}
	},
	{
		timestamps: true
	}
);

const Product =
	mongoose.models.Product || mongoose.model<IProduct>('Product', ProductSchema);

export default Product;
