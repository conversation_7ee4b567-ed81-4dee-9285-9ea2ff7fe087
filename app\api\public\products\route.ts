import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongoose';
import Product from '@/models/Product';
import { withApiAuth } from '@/app/api/middleware';
import { withCors } from '@/app/api/cors-middleware';
import { serializeData } from '@/lib/serialization';

export const dynamic = 'force-dynamic';

/**
 * Handler for the products API endpoint
 * Returns all published products in a simplified format
 */
async function handler(req: Request, apiToken: any) {
  console.log('Public products API endpoint called with token:', apiToken ? apiToken._id : 'unknown');
  console.log('Token usage count:', apiToken ? apiToken.usageCount : 'unknown');
  try {
    // Connect to the database
    await connectToDatabase();

    // Get query parameters
    const { searchParams } = new URL(req.url);
    const featured = searchParams.get('featured');
    const status = searchParams.get('status');

    // Build the query
    let query: any = {};

    if (featured === 'true') {
      query.isFeatured = true;
    }

    if (status) {
      query.status = status;
    } else {
      // By default, only return published products
      query.status = 'Published';
    }

    // Fetch products
    const products = await Product.find(query).sort({ order: 1 }).lean();

    // Transform products to the requested format
    const formattedProducts = products.map(product => ({
      id: product._id ? product._id.toString() : '',
      title: product.title,
      description: product.description,
      price: product.price,
      media: product.media,
      assetStoreLink: product.externalLink,
      liveDemoLink: product.liveDemoLink,
      status: product.status,
      createdAt: product.createdAt,
      updatedAt: product.updatedAt
    }));

    return NextResponse.json(serializeData(formattedProducts));
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}

// All endpoints require API token authentication
export const GET = (req: NextRequest) => withCors(req, (req) => withApiAuth(req, handler));
export const POST = (req: NextRequest) => withCors(req, (req) => withApiAuth(req, handler));

// Handle OPTIONS requests for CORS
export const OPTIONS = (req: NextRequest) => withCors(req, async () => new NextResponse(null, { status: 204 }));
