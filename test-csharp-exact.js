// Test script to simulate the exact C# API call format
import fetch from 'node-fetch';

// Use the token provided by the user
const API_TOKEN = '321f37d68f91dc9951c368f732566f452a19bd7d7657c857e7b02bdc8c1c0367';
const API_BASE_URL = 'http://localhost:3000/api';

async function testApiCall() {
  try {
    console.log('Testing API call with exact C# format...');
    
    // Make the API request using the exact same format as the C# code
    const url = `${API_BASE_URL}/products`;
    console.log('URL:', url);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`
      }
    });
    
    const data = await response.json();
    
    if (response.ok) {
      console.log('API call successful!');
      console.log('Number of products returned:', Array.isArray(data) ? data.length : 'Not an array');
    } else {
      console.error('API request failed with status', response.status);
      console.error('Error message:', data.error || 'Unknown error');
    }
  } catch (error) {
    console.error('Error making API call:', error);
  }
}

testApiCall();
