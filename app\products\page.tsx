import { Suspense } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ProductCard } from '@/components/Product/ProductCard';
import { IProduct } from '@/models/Product';
import { Status } from '@/types/global';
import { connectToDatabase } from '@/lib/mongoose';
import Product from '@/models/Product';
import { serializeData } from '@/lib/serialization';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';

export const dynamic = 'force-dynamic';
export const revalidate = 0;

// Skeleton loader component
const ProductSkeleton = () => (
	<div className="animate-pulse">
		<div className="h-8 bg-muted rounded-md w-3/4 mb-4"></div>
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-items-center">
			{[...Array(6)].map((_, i) => (
				<Card key={i} className="flex flex-col w-full max-w-sm relative animate-pulse">
					<CardHeader>
						<div className="h-6 bg-muted rounded w-3/4"></div>
					</CardHeader>
					<CardContent className="flex-grow">
						<div className="relative w-full h-48 mb-4 bg-muted rounded-md"></div>
						<div className="mt-2 mb-3">
							<div className="h-4 bg-muted rounded w-full mb-2"></div>
							<div className="h-4 bg-muted rounded w-5/6"></div>
						</div>
						<div className="h-6 bg-muted rounded w-1/4 mt-2"></div>
					</CardContent>
					<CardFooter>
						<div className="h-10 bg-muted rounded w-full"></div>
					</CardFooter>
				</Card>
			))}
		</div>
	</div>
);

async function getProducts(): Promise<IProduct[]> {
	await connectToDatabase();
	const products = await Product.find({ status: Status.Published })
		.sort({ order: 1 })
		.lean();

	// Use our serialization utility to handle all MongoDB objects
	return serializeData(products);
}

async function ProductContent() {
	const products = await getProducts();
	if (products.length === 0) {
		return (
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-items-center">
				{[1, 2, 3].map((i) => (
					<Card key={i} className="flex flex-col w-full max-w-sm relative">
						<CardHeader>
							<div className="text-lg font-semibold">No Product Available</div>
						</CardHeader>
						<CardContent className="flex-grow">
							<div className="relative w-full h-48 mb-4 bg-muted flex items-center justify-center rounded-md">
								<span className="text-muted-foreground">No image available</span>
							</div>
							<div className="mt-2 mb-3 text-sm text-muted-foreground">
								No published products available at the moment. Check back later!
							</div>
						</CardContent>
						<CardFooter>
							<div className="h-6 bg-muted rounded w-1/4"></div>
						</CardFooter>
					</Card>
				))}
			</div>
		);
	}

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-items-center">
			{products.map((product) => (
				<ProductCard key={product._id.toString()} product={product} />
			))}
		</div>
	);
}

export default function ProductsPage() {
	return (
		<div className="container mx-auto px-4 py-8">
			<h1 className="text-4xl font-bold mb-12 text-center relative pb-4 after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:-translate-x-1/2 after:w-24 after:h-1 after:bg-primary after:rounded-full">Products</h1>
			<Suspense fallback={<ProductSkeleton />}>
				<ProductContent />
			</Suspense>
		</div>
	);
}
