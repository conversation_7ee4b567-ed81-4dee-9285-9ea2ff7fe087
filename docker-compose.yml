version: '3.9'

services:
  nextjs-app:
    image: 192.168.0.59:6500/realsoftgames:latest
    container_name: realsoftgames
    restart: unless-stopped
    ports:
      - "3040:3000"
    environment:
      # Application Environment
      - NODE_ENV=production
      - NEXT_PUBLIC_URL=https://realsoftgames.com
      - JWT_SECRET=${JWT_SECRET}

      # Database Configuration
      - MONGODB_URI=${MONGODB_URI}

      # Email Configuration
      - GMAIL_USER=${GMAIL_USER}
      - GMAIL_APP_PASSWORD=${GMAIL_APP_PASSWORD}

      # MinIO Configuration
      - MINIO_ENDPOINT=${MINIO_ENDPOINT}
      - MINIO_PUBLIC_URL=${MINIO_PUBLIC_URL}
      - MINIO_ACCESS_KEY=${MINIO_ACCESS_KEY}
      - MINIO_SECRET_KEY=${MINIO_SECRET_KEY}
      - MINIO_BUCKET=${MINIO_BUCKET}
      - MINIO_REGION=${MINIO_REGION}
      - MINIO_PATH_STYLE=${MINIO_PATH_STYLE}
    networks:
      - bridge

networks:
  bridge:
    driver: bridge