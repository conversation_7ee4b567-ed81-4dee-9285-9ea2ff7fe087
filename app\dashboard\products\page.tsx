'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { IProduct } from '@/models/Product';
import { Status, StatusFilter } from '@/types/global';
import ProductForm from '@/components/Product/ProductForm';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import { useToast } from '@/hooks/use-toast';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from '@/components/ui/select';
import { ChevronUp, ChevronDown } from 'lucide-react';
import ClientImage from '@/components/ClientImage';

interface ProductListProps {
	products: IProduct[];
	onEdit: (id: string) => void;
	onDelete: (id: string) => void;
	onChangeStatus: (id: string, newStatus: Status) => void;
	onToggleFeatured: (id: string) => void;
	onReorder: (newProducts: IProduct[]) => void;
}

interface DraggableProductCardProps {
	product: IProduct;
	index: number;
	moveProduct: (dragIndex: number, hoverIndex: number) => void;
	onEdit: (id: string) => void;
	onDelete: (id: string) => void;
	onChangeStatus: (id: string, newStatus: Status) => void;
	onToggleFeatured: (id: string) => void;
}

function DraggableProductCard({
	product,
	index,
	moveProduct,
	onEdit,
	onDelete,
	onChangeStatus,
	onToggleFeatured,
	isMobile,
	totalItems
}: DraggableProductCardProps & { isMobile: boolean; totalItems: number }) {
	const ref = useRef<HTMLDivElement>(null);
	const [, drop] = useDrop<
		{ index: number },
		void,
		{ handlerId: string | symbol | null }
	>({
		accept: 'product',
		hover(item: { index: number }, monitor) {
			if (!ref.current) {
				return;
			}
			const dragIndex = item.index;
			const hoverIndex = index;
			if (dragIndex === hoverIndex) {
				return;
			}
			moveProduct(dragIndex, hoverIndex);
			item.index = hoverIndex;
		}
	});

	const [{ isDragging }, drag] = useDrag({
		type: 'product',
		item: () => ({ id: product._id, index }),
		collect: (monitor) => ({
			isDragging: monitor.isDragging()
		})
	});

	drag(drop(ref));

	return (
		<div ref={ref} style={{ opacity: isDragging ? 0.5 : 1 }}>
			<Card key={product._id.toString()} className="flex flex-col">
				<CardHeader>
					<div className="flex justify-between items-start">
						<div className="flex items-center gap-2">
							<div className={`w-3 h-3 rounded-full ${product.status === Status.Published ? 'bg-green-500' : product.status === Status.Draft ? 'bg-yellow-500' : 'bg-red-500'}`}
								title={product.status}
							/>
							<CardTitle className="text-lg">{product.title}</CardTitle>
						</div>
						<div className="flex flex-col items-end space-y-1">
							<Badge
								variant={
									product.status === Status.Published ? 'default' : 'secondary'
								}
							>
								{product.status}
							</Badge>
							{product.isFeatured && <Badge variant="warning">Featured</Badge>}
						</div>
					</div>
				</CardHeader>
				<CardContent className="flex-grow">
					{product.media && product.media.length > 0 && (
						<div className="relative w-full h-40 mb-4">
							{product.media[0].type === 'image' ? (
								<ClientImage
									src={product.media[0].url}
									alt={product.title}
									className="rounded-md w-full h-full object-cover"
									style={{ objectFit: 'cover' }}
								/>
							) : (
								<iframe
									src={product.media[0].url}
									title={product.title}
									className="w-full h-full rounded-md"
									allowFullScreen
								/>
							)}
						</div>
					)}
					<Badge variant="success" className="mt-2">
						{Number(product.price) === 0 ? 'Free' : `$${product.price}`}
					</Badge>
				</CardContent>
				<div className="p-4 bg-muted/50 flex justify-between items-center">
					<div className="flex space-x-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => onEdit(product._id.toString())}
						>
							Edit
						</Button>
						{product.status === Status.Archived ? (
							<>
								<Button
									variant="outline"
									size="sm"
									onClick={() =>
										onChangeStatus(product._id.toString(), Status.Draft)
									}
								>
									Restore
								</Button>
								<AlertDialog>
									<AlertDialogTrigger asChild>
										<Button variant="destructive" size="sm">
											Delete Permanently
										</Button>
									</AlertDialogTrigger>
									<AlertDialogContent>
										<AlertDialogHeader>
											<AlertDialogTitle>
												Are you absolutely sure?
											</AlertDialogTitle>
											<AlertDialogDescription>
												This action cannot be undone. This will permanently
												delete the product and remove it from our servers.
											</AlertDialogDescription>
										</AlertDialogHeader>
										<AlertDialogFooter>
											<AlertDialogCancel>Cancel</AlertDialogCancel>
											<AlertDialogAction
												onClick={() => onDelete(product._id.toString())}
											>
												Yes, delete permanently
											</AlertDialogAction>
										</AlertDialogFooter>
									</AlertDialogContent>
								</AlertDialog>
							</>
						) : (
							<Button
								variant="outline"
								size="sm"
								onClick={() =>
									onChangeStatus(product._id.toString(), Status.Archived)
								}
							>
								Archive
							</Button>
						)}
					</div>
					{isMobile && (
						<div className="flex flex-col space-y-1">
							<Button
								variant="outline"
								size="sm"
								onClick={() => moveProduct(index, index - 1)}
								disabled={index === 0}
							>
								<ChevronUp className="h-4 w-4" />
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={() => moveProduct(index, index + 1)}
								disabled={index === totalItems - 1}
							>
								<ChevronDown className="h-4 w-4" />
							</Button>
						</div>
					)}
				</div>
			</Card>
		</div>
	);
}

function ProductList({
	products,
	onEdit,
	onDelete,
	onChangeStatus,
	onToggleFeatured,
	onReorder,
	isMobile
}: ProductListProps & { isMobile: boolean }) {
	const moveProduct = useCallback(
		(dragIndex: number, hoverIndex: number) => {
			if (
				dragIndex < 0 ||
				hoverIndex < 0 ||
				dragIndex >= products.length ||
				hoverIndex >= products.length
			) {
				return;
			}
			onReorder(
				update(products, {
					$splice: [
						[dragIndex, 1],
						[hoverIndex, 0, products[dragIndex]]
					]
				})
			);
		},
		[products, onReorder]
	);

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
			{products.map((product, index) => (
				<DraggableProductCard
					key={product._id.toString()}
					product={product}
					index={index}
					moveProduct={moveProduct}
					onEdit={onEdit}
					onDelete={onDelete}
					onChangeStatus={onChangeStatus}
					onToggleFeatured={onToggleFeatured}
					isMobile={isMobile}
					totalItems={products.length}
				/>
			))}
		</div>
	);
}

export default function ProductsPage() {
	const [products, setProducts] = useState<IProduct[]>([]);
	const [activeTab, setActiveTab] = useState<StatusFilter>(StatusFilter.All);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [showForm, setShowForm] = useState(false);
	const [editingProduct, setEditingProduct] = useState<IProduct | null>(null);
	const { toast } = useToast();
	const [originalOrder, setOriginalOrder] = useState<IProduct[]>([]);
	const [hasOrderChanged, setHasOrderChanged] = useState(false);
	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		fetchProducts();
		const checkIfMobile = () => setIsMobile(window.innerWidth < 768);
		checkIfMobile();
		window.addEventListener('resize', checkIfMobile);

		// Check for 'new' query parameter
		const urlParams = new URLSearchParams(window.location.search);
		if (urlParams.get('new') === 'true') {
			setShowForm(true);
			// Update URL without refreshing the page
			const newUrl = window.location.pathname;
			window.history.pushState({}, '', newUrl);
		}

		return () => window.removeEventListener('resize', checkIfMobile);
	}, []);

	const fetchProducts = async () => {
		setIsLoading(true);
		setError(null);
		try {
			// Add credentials: 'include' to ensure cookies are sent with the request
			const response = await fetch('/api/products', {
				credentials: 'include'
			});

			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				throw new Error(errorData.error || 'Failed to fetch products');
			}

			const data = await response.json();
			// Sort products by order
			const sortedProducts = data.sort(
				(a: IProduct, b: IProduct) => a.order - b.order
			);
			setProducts(sortedProducts);
			setOriginalOrder(sortedProducts); // Set the original order here
		} catch (error) {
			console.error('Error fetching products:', error);
			setError(error instanceof Error ? error.message : 'Failed to load products. Please try again.');
			toast({
				title: 'Error',
				description: error instanceof Error ? error.message : 'Failed to load products',
				type: 'error'
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleDelete = async (id: string) => {
		try {
			const response = await fetch(`/api/products/${id}`, {
				method: 'DELETE',
				credentials: 'include'
			});
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to delete product');
			}
			setProducts(products.filter((product) => product._id.toString() !== id));
			toast({
				title: 'Success',
				description: 'Product and associated media deleted successfully',
				type: 'success'
			});
		} catch (error) {
			console.error('Error deleting product:', error);
			toast({
				title: 'Error',
				description:
					error instanceof Error
						? error.message
						: 'Failed to delete product and associated media. Please try again.',
				type: 'error'
			});
		}
	};

	const handleEdit = (id: string) => {
		const productToEdit = products.find((p) => p._id.toString() === id);
		if (productToEdit) {
			setEditingProduct(productToEdit);
			setShowForm(true);
		}
	};

	const handleChangeStatus = async (id: string, newStatus: Status) => {
		try {
			const response = await fetch(`/api/products/${id}`, {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ status: newStatus }),
				credentials: 'include'
			});
			if (!response.ok) {
				throw new Error('Failed to update product status');
			}
			const updatedProduct = await response.json();
			setProducts(
				products.map((product) =>
					product._id.toString() === id ? updatedProduct : product
				)
			);
			toast({
				title: 'Success',
				description: `Product ${
					newStatus === Status.Archived ? 'archived' : 'status updated'
				} successfully`,
				type: 'success'
			});
		} catch (error) {
			console.error('Error updating product status:', error);
			toast({
				title: 'Error',
				description: 'Failed to update product status. Please try again.',
				type: 'error'
			});
		}
	};

	const handleToggleFeatured = async (id: string) => {
		try {
			const product = products.find((p) => p._id.toString() === id);
			if (!product) return;

			const response = await fetch(`/api/products/${id}`, {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ isFeatured: !product.isFeatured }),
				credentials: 'include'
			});
			if (!response.ok) {
				throw new Error('Failed to toggle featured status');
			}
			setProducts(
				products.map((p) =>
					p._id.toString() === id
						? ({ ...p, isFeatured: !p.isFeatured } as IProduct)
						: p
				)
			);
			toast({
				title: 'Success',
				description: `Product ${
					product.isFeatured ? 'unfeatured' : 'featured'
				} successfully`,
				type: 'success'
			});
		} catch (error) {
			console.error('Error toggling featured status:', error);
			toast({
				title: 'Error',
				description: 'Failed to update featured status. Please try again.',
				type: 'error'
			});
		}
	};

	const handleCreateOrUpdateProduct = async (
		productData: Partial<IProduct>
	) => {
		try {
			const url = editingProduct
				? `/api/products/${editingProduct._id}`
				: '/api/products';
			const method = editingProduct ? 'PATCH' : 'POST';

			const response = await fetch(url, {
				method,
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(productData),
				credentials: 'include'
			});

			if (!response.ok) {
				const errorData = await response.json();
				console.error('Error response:', errorData);
				throw new Error(
					errorData.error ||
						`Failed to ${editingProduct ? 'update' : 'create'} product`
				);
			}

			const updatedProduct = await response.json();

			if (editingProduct) {
				setProducts(
					products.map((p) =>
						p._id === updatedProduct._id ? updatedProduct : p
					)
				);
				toast({
					title: 'Success',
					description: 'Product updated successfully',
					type: 'success'
				});
			} else {
				setProducts([...products, updatedProduct]);
				toast({
					title: 'Success',
					description: 'Product created successfully',
					type: 'success'
				});
			}

			setShowForm(false);
			setEditingProduct(null);
		} catch (error) {
			console.error(
				`Error ${editingProduct ? 'updating' : 'creating'} product:`,
				error
			);
			toast({
				title: 'Error',
				description:
					error instanceof Error
						? error.message
						: `Failed to ${
								editingProduct ? 'update' : 'create'
						  } product. Please try again.`,
				type: 'error'
			});
		}
	};

	const handleCancelForm = () => {
		setShowForm(false);
		setEditingProduct(null);
	};

	const handleReorder = useCallback((newProducts: IProduct[]) => {
		setProducts(newProducts);
		setHasOrderChanged(true);
	}, []);

	const handleSaveOrder = async () => {
		try {
			const updatedProducts = products.map((product, index) => ({
				...product,
				order: index
			}));

			// Update the order in the database
			await Promise.all(
				updatedProducts.map((product) =>
					fetch(`/api/products/${product._id}`, {
						method: 'PATCH',
						headers: { 'Content-Type': 'application/json' },
						body: JSON.stringify({ order: product.order }),
						credentials: 'include'
					})
				)
			);

			// Fetch the updated products from the server
			const response = await fetch('/api/products', {
				credentials: 'include'
			});
			if (!response.ok) {
				throw new Error('Failed to fetch updated products');
			}
			const updatedProductsFromServer: IProduct[] = await response.json();

			// Sort the fetched products by the new order
			const sortedProducts = updatedProductsFromServer.sort(
				(a, b) => a.order - b.order
			);

			setProducts(sortedProducts);
			setOriginalOrder(sortedProducts);
			setHasOrderChanged(false);
			toast({
				title: 'Success',
				description: 'Product order updated successfully',
				type: 'success'
			});
		} catch (error) {
			console.error('Error updating product order:', error);
			toast({
				title: 'Error',
				description: 'Failed to update product order. Please try again.',
				type: 'error'
			});
		}
	};

	const handleCancelReorder = () => {
		setProducts(originalOrder);
		setHasOrderChanged(false);
	};

	return (
		<DndProvider backend={HTML5Backend}>
			<div className="container mx-auto px-4 py-8">
				<div className="flex justify-between items-center mb-4">
					<h1 className="text-2xl font-bold">Your Products</h1>
					{!showForm && (
						<Button onClick={() => setShowForm(true)}>
							Create New Product
						</Button>
					)}
				</div>
				{showForm ? (
					<div className="mt-8">
						<h2 className="text-xl font-semibold mb-4">
							{editingProduct ? 'Edit Product' : 'Create New Product'}
						</h2>
						<ProductForm
							initialData={editingProduct}
							onSubmit={handleCreateOrUpdateProduct}
							onCancel={handleCancelForm}
						/>
					</div>
				) : (
					<>
						{isMobile ? (
							<Select
								onValueChange={(value) => setActiveTab(value as StatusFilter)}
								defaultValue={activeTab}
							>
								<SelectTrigger className="w-full mb-4">
									<SelectValue placeholder="Select status" />
								</SelectTrigger>
								<SelectContent>
									{Object.values(StatusFilter).map((status) => (
										<SelectItem key={status} value={status}>
											{status} Products
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						) : (
							<Tabs
								value={activeTab}
								onValueChange={(value) => setActiveTab(value as StatusFilter)}
							>
								<TabsList className="flex flex-col sm:flex-row">
									{Object.values(StatusFilter).map((status) => (
										<TabsTrigger
											key={status}
											value={status}
											className="flex-grow"
										>
											{status} Products
										</TabsTrigger>
									))}
								</TabsList>
							</Tabs>
						)}
						<div className="mt-4">
							{isLoading ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
									{[...Array(6)].map((_, i) => (
										<Card key={i} className="flex flex-col w-full relative animate-pulse">
											<CardHeader>
												<div className="flex justify-between items-start">
													<div className="flex items-center gap-2">
														<div className="w-3 h-3 rounded-full bg-muted"></div>
														<div className="h-6 bg-muted rounded w-3/4"></div>
													</div>
													<div className="h-6 bg-muted rounded w-20"></div>
												</div>
											</CardHeader>
											<CardContent className="flex-grow">
												<div className="relative w-full h-48 mb-4 bg-muted rounded-md"></div>
												<div className="mt-2 mb-3">
													<div className="h-4 bg-muted rounded w-full mb-2"></div>
													<div className="h-4 bg-muted rounded w-5/6"></div>
												</div>
												<div className="h-6 bg-muted rounded w-1/4 mt-2"></div>
											</CardContent>
											<div className="p-4 bg-muted/20 flex justify-between items-center">
												<div className="flex space-x-2">
													<div className="h-10 bg-muted rounded w-16"></div>
													<div className="h-10 bg-muted rounded w-20"></div>
												</div>
											</div>
										</Card>
									))}
								</div>
							) : error ? (
								<Alert>
									<AlertDescription>{error}</AlertDescription>
								</Alert>
							) : (
								<>
									{products.length === 0 ? (
										<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
											{[1, 2, 3].map((i) => (
												<Card key={i} className="flex flex-col relative">
													<CardHeader>
														<div className="text-lg font-semibold">No Products Available</div>
													</CardHeader>
													<CardContent className="flex-grow">
														<div className="relative w-full h-40 mb-4 bg-muted flex items-center justify-center rounded-md">
															<span className="text-muted-foreground">No image available</span>
														</div>
														<div className="mt-2 mb-3 text-sm text-muted-foreground">
															No products available. Create a new product to get started.
														</div>
													</CardContent>
													<div className="p-4 bg-muted/20 flex justify-center">
														<Button onClick={() => setShowForm(true)}>Create New Product</Button>
													</div>
												</Card>
											))}
										</div>
									) : (
										<ProductList
											products={activeTab === StatusFilter.All
												? products
												: products.filter((product) => product.status === activeTab as unknown as Status)
											}
											onEdit={handleEdit}
											onDelete={handleDelete}
											onChangeStatus={handleChangeStatus}
											onToggleFeatured={handleToggleFeatured}
											onReorder={handleReorder}
											isMobile={isMobile}
										/>
									)}
								</>
							)}
						</div>
						{hasOrderChanged && (
							<div className="mt-4 flex justify-end space-x-2">
								<Button onClick={handleCancelReorder} variant="outline">
									Cancel
								</Button>
								<Button onClick={handleSaveOrder}>Save Order</Button>
							</div>
						)}
					</>
				)}
			</div>
		</DndProvider>
	);
}
