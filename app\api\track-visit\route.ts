import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { connectToDatabase } from '@/lib/mongoose'

export async function POST(request: Request) {
  try {
    await connectToDatabase()
    const userAgent = request.headers.get('user-agent') || 'Unknown'

    const db = await connectToDatabase()
    await db.connection.collection('visitors').insertOne({
      userAgent,
      timestamp: new Date()
    })

    return NextResponse.json({ message: 'Visit tracked successfully' })
  } catch (error) {
    console.error('Error tracking visit:', error)
    return NextResponse.json({ message: 'An error occurred while tracking the visit' }, { status: 500 })
  }
}