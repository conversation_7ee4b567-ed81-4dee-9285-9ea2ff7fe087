import { NextResponse } from 'next/server';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { connectToDatabase } from '@/lib/mongoose';
import User from '@/models/User';
import crypto from 'crypto';
import { sendVerificationEmail } from '@/lib/email';

export async function POST(request: Request) {
  try {
    // Await authOptions to properly handle cookies and headers
    const options = await authOptions();
    const session = await getServerSession(options);
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    await connectToDatabase();
    const { email } = await request.json();

    const user = await User.findById(session.user.id);
    if (!user) {
      return NextResponse.json({ message: 'User not found' }, { status: 404 });
    }

    // Check if the new email is already in use
    const existingUser = await User.findOne({ email });
    if (existingUser && existingUser._id.toString() !== session.user.id) {
      return NextResponse.json({ message: 'Email is already in use' }, { status: 400 });
    }

    const token = crypto.randomBytes(32).toString('hex');
    user.emailChangeToken = token;
    user.newEmail = email;
    await user.save();

    await sendVerificationEmail(email, token);

    return NextResponse.json({ message: 'Verification email sent' });
  } catch (error) {
    console.error('Email update error:', error);
    return NextResponse.json({ message: 'An error occurred while updating the email' }, { status: 500 });
  }
}