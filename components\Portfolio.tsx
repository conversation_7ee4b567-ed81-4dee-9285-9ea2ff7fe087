import { useSession } from 'next-auth/react';
import { Avatar } from './Avatar';
import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

export function Portfolio() {
	const { data: session, update } = useSession();
	const [isUploading, setIsLoading] = useState(false);
	const [avatarKey, setAvatarKey] = useState(Date.now());
	const { toast } = useToast();
	const fileInputRef = useRef<HTMLInputElement>(null);

	useEffect(() => {
		console.log('Session data:', session);
		console.log('User avatar:', session?.user?.avatar);
	}, [session]);

	if (!session?.user) return null;

	const handleAvatarUpload = async (
		event: React.ChangeEvent<HTMLInputElement>
	) => {
		const file = event.target.files?.[0];
		if (!file) return;

		setIsLoading(true);
		const formData = new FormData();
		formData.append('file', file);

		try {
			const uploadResponse = await fetch('/api/upload?type=avatar', {
				method: 'POST',
				body: formData
			});

			if (!uploadResponse.ok) {
				throw new Error('Failed to upload avatar');
			}

			const uploadData = await uploadResponse.json();
			console.log('Upload response:', uploadData);

			const updateResponse = await fetch('/api/user/update-avatar', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ avatarUrl: uploadData.url })
			});

			if (!updateResponse.ok) {
				throw new Error('Failed to update user avatar');
			}

			const updateData = await updateResponse.json();
			console.log('Update response:', updateData);

			// Force a complete session refresh
			const result = await update({ avatar: updateData.avatarUrl });
			console.log('Session update result:', result);

			// Manually update the session data
			if (session && session.user) {
				session.user.avatar = updateData.avatarUrl;
			}

			// Trigger a re-render
			setAvatarKey(Date.now());

			toast({
				title: 'Success',
				description: 'Avatar uploaded and updated successfully',
				type: 'success'
			});

			console.log('Updated session:', session);
			console.log('Updated user avatar:', session.user.avatar);
		} catch (error) {
			console.error('Error uploading avatar:', error);
			toast({
				title: 'Error',
				description: 'Failed to upload or update avatar',
				type: 'error'
			});
		} finally {
			setIsLoading(false);
		}
	};

	const triggerFileInput = () => {
		fileInputRef.current?.click();
	};

	return (
		<div className="flex flex-col items-center space-y-4 p-6 bg-white dark:bg-gray-800 rounded-lg shadow">
			<Avatar
				key={avatarKey}
				src={
					session.user.avatar
						? `${session.user.avatar}?${avatarKey}`
						: undefined
				}
				fallback={session.user.username}
				size="lg"
				className="mb-4"
			/>
			<h2 className="text-2xl font-bold text-gray-900 dark:text-white">
				{session.user.username}
			</h2>
			<input
				type="file"
				accept="image/*"
				onChange={handleAvatarUpload}
				disabled={isUploading}
				className="hidden"
				ref={fileInputRef}
			/>
			<Button
				onClick={triggerFileInput}
				disabled={isUploading}
				className="bg-gray-200 text-gray-900 hover:bg-gray-300 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600"
			>
				{isUploading ? 'Uploading...' : 'Change Avatar'}
			</Button>
		</div>
	);
}
