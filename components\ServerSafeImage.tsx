'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface ServerSafeImageProps {
  src: string | null | undefined;
  alt?: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  sizes?: string;
  style?: React.CSSProperties;
  fill?: boolean;
}

export default function ServerSafeImage({
  src,
  alt,
  className,
  ...props
}: ServerSafeImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [imageSrc, setImageSrc] = useState<string>('');
  const [error, setError] = useState(false);

  // Process the URL for MinIO images on component mount
  useEffect(() => {
    if (!src) {
      setIsLoading(false);
      return;
    }

    // Use the direct URL for now to debug the issue
    setImageSrc(src);
    console.log('ServerSafeImage: Using direct URL:', src);
  }, [src]);

  const handleError = () => {
    console.error(`Failed to load image: ${src}`);
    setError(true);
    setIsLoading(false);
  };

  // If no source is available or there was an error, show a loading placeholder
  if (!imageSrc || error) {
    return (
      <div className={`relative ${className || ''}`} style={props.style || { height: '100%', width: '100%' }}>
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200 dark:bg-gray-700 rounded">
          <span className="text-gray-500 dark:text-gray-400 text-sm">Image not available</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className || ''}`} style={props.style}>
      {props.fill ? (
        <Image
          {...props}
          className={`${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300 ${props.className || ''}`}
          src={imageSrc}
          alt={alt || 'Image'}
          onLoad={() => setIsLoading(false)}
          onError={handleError}
          fill
          sizes={props.sizes || '100vw'}
          unoptimized={true}
        />
      ) : (
        <Image
          {...props}
          className={`${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300 ${props.className || ''}`}
          src={imageSrc}
          alt={alt || 'Image'}
          onLoad={() => setIsLoading(false)}
          onError={handleError}
          width={props.width || 500}
          height={props.height || 300}
          unoptimized={true}
        />
      )}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200 dark:bg-gray-700 animate-pulse rounded">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
}
