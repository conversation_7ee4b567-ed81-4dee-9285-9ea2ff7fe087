import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export const runtime = 'nodejs';

export async function GET(
  req: Request,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    // Get the path parameters
    const { path: pathParts } = await params;

    if (pathParts.length < 2) {
      return NextResponse.json(
        { error: 'Invalid path format. Expected /minio/bucket/key' },
        { status: 400 }
      );
    }

    // Extract bucket and key from path
    const bucket = pathParts[0];
    const key = pathParts.slice(1).join('/');

    console.log(`Proxying Min<PERSON> request for bucket: ${bucket}, key: ${key}`);

    // Construct the MinIO URL using environment variables
    const useSSL = process.env.MINIO_USE_SSL === 'true';
    const endpoint = process.env.MINIO_PUBLIC_URL ||
                    `http${useSSL ? 's' : ''}://${process.env.MINIO_ENDPOINT}${
                      process.env.MINIO_PORT ? `:${process.env.MINIO_PORT}` : ''
                    }`;
    const minioUrl = `${endpoint}/${bucket}/${key}`;

    console.log(`Fetching from MinIO URL: ${minioUrl}`);

    // Fetch the image from MinIO with retry logic
    let response;
    let retries = 3;

    while (retries > 0) {
      try {
        response = await fetch(minioUrl, {
          headers: {
            'Accept': 'image/*',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Cache-Control': 'max-age=31536000' // Request caching for 1 year
          },
          // Use force-cache for better performance
          cache: 'force-cache',
          next: { revalidate: 604800 } // Revalidate once per week for better performance
        });

        if (response.ok) break;

        console.warn(`Retry ${4 - retries}/3: Failed to fetch from MinIO: ${response.status} ${response.statusText}`);
        retries--;

        if (retries > 0) {
          // Wait before retrying (exponential backoff)
          await new Promise(resolve => setTimeout(resolve, (4 - retries) * 500));
        }
      } catch (fetchError) {
        console.error(`Fetch error: ${fetchError instanceof Error ? fetchError.message : 'Unknown error'}`);
        retries--;

        if (retries <= 0) throw fetchError;

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, (4 - retries) * 500));
      }
    }

    if (!response || !response.ok) {
      console.error(`Failed to fetch from MinIO after retries: ${response?.status} ${response?.statusText}`);
      return NextResponse.json(
        { error: `Failed to fetch from MinIO: ${response?.statusText || 'Unknown error'}` },
        { status: response?.status || 500 }
      );
    }

    // Get the image data as an array buffer
    const imageData = await response.arrayBuffer();

    // Create a new response with the image data
    const imageResponse = new NextResponse(imageData);

    // Copy the content type and other relevant headers
    const contentType = response.headers.get('Content-Type');
    imageResponse.headers.set('Content-Type', contentType || 'image/jpeg');
    imageResponse.headers.set('Content-Length', response.headers.get('Content-Length') || String(imageData.byteLength));
    // Set aggressive caching headers for better performance
    imageResponse.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
    imageResponse.headers.set('Access-Control-Allow-Origin', '*');
    imageResponse.headers.set('X-Cache-Optimized', 'true');

    return imageResponse;
  } catch (error) {
    console.error('Error proxying MinIO image:', error);
    return NextResponse.json(
      { error: 'Failed to proxy MinIO image' },
      { status: 500 }
    );
  }
}
