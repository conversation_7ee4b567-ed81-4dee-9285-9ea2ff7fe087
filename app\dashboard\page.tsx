'use client';

import { useSession } from 'next-auth/react';
import { UserRole } from '@/types/global';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useState, useEffect } from 'react';
import { FolderPlus, ShoppingBag, Users, FolderOpen, User, Key, BarChart } from 'lucide-react';
import Link from 'next/link';

interface DashboardStats {
	projects: number;
	products: number;
	users: number;
	apiTokens: number;
	apiUsage: number;
}

export default function DashboardPage() {
	const { data: session } = useSession();
	const isAdmin = session?.user?.role === UserRole.ADMIN;
	const [stats, setStats] = useState<DashboardStats>({
		projects: 0,
		products: 0,
		users: 0,
		apiTokens: 0,
		apiUsage: 0
	});

	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		const fetchStats = async () => {
			try {
				setIsLoading(true);
				const response = await fetch('/api/dashboard/stats');
				if (!response.ok) {
					throw new Error('Failed to fetch dashboard stats');
				}
				const data = await response.json();
				setStats(data);
			} catch (error) {
				// Error handled silently
			} finally {
				setIsLoading(false);
			}
		};

		fetchStats();
	}, []);

	if (!session) {
		return <div>Loading...</div>;
	}

	return (
		<div className="space-y-8">
			<div className="flex justify-between items-center">
				<h1 className="text-2xl font-bold">{isAdmin ? 'Admin Dashboard' : 'Dashboard'}</h1>
				{isAdmin && (
					<div className="flex space-x-2">
						<Link href="/dashboard/projects?new=true">
							<Button size="sm">
								<FolderPlus className="mr-2 h-4 w-4" />
								Add Project
							</Button>
						</Link>
						<Link href="/dashboard/products?new=true">
							<Button size="sm">
								<ShoppingBag className="mr-2 h-4 w-4" />
								Add Product
							</Button>
						</Link>
					</div>
				)}
			</div>

			{/* Dashboard Grid */}
			{isAdmin ? (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6">
					{/* Projects Card */}
					<Card className="relative overflow-hidden">
						<div className="absolute right-2 top-2 p-2 bg-muted rounded-full">
							<FolderOpen className="h-5 w-5 text-muted-foreground" />
						</div>
						<CardHeader className="pb-2">
							<CardTitle className="text-lg font-medium">Projects</CardTitle>
							<p className="text-sm text-muted-foreground">Manage your portfolio projects</p>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading ? (
									<span className="text-muted-foreground">Loading...</span>
								) : (
									`${stats.projects} projects available`
								)}
							</div>
						</CardContent>
					</Card>

					{/* Products Card */}
					<Card className="relative overflow-hidden">
						<div className="absolute right-2 top-2 p-2 bg-muted rounded-full">
							<ShoppingBag className="h-5 w-5 text-muted-foreground" />
						</div>
						<CardHeader className="pb-2">
							<CardTitle className="text-lg font-medium">Products</CardTitle>
							<p className="text-sm text-muted-foreground">Manage your products</p>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading ? (
									<span className="text-muted-foreground">Loading...</span>
								) : (
									`${stats.products} products available`
								)}
							</div>
						</CardContent>
					</Card>

					{/* Users Card */}
					<Card className="relative overflow-hidden">
						<div className="absolute right-2 top-2 p-2 bg-muted rounded-full">
							<Users className="h-5 w-5 text-muted-foreground" />
						</div>
						<CardHeader className="pb-2">
							<CardTitle className="text-lg font-medium">Users</CardTitle>
							<p className="text-sm text-muted-foreground">Manage user accounts</p>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading ? (
									<span className="text-muted-foreground">Loading...</span>
								) : (
									`${stats.users} users registered`
								)}
							</div>
						</CardContent>
					</Card>

					{/* API Tokens Card */}
					<Card className="relative overflow-hidden">
						<div className="absolute right-2 top-2 p-2 bg-muted rounded-full">
							<Key className="h-5 w-5 text-muted-foreground" />
						</div>
						<CardHeader className="pb-2">
							<CardTitle className="text-lg font-medium">API Tokens</CardTitle>
							<p className="text-sm text-muted-foreground">Manage API access</p>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading ? (
									<span className="text-muted-foreground">Loading...</span>
								) : (
									`${stats.apiTokens} tokens active`
								)}
							</div>
							<Link href="/dashboard/api-tokens" className="text-sm text-primary hover:underline mt-2 inline-block">
								Manage tokens
							</Link>
						</CardContent>
					</Card>

					{/* API Usage Card */}
					<Card className="relative overflow-hidden">
						<div className="absolute right-2 top-2 p-2 bg-muted rounded-full">
							<BarChart className="h-5 w-5 text-muted-foreground" />
						</div>
						<CardHeader className="pb-2">
							<CardTitle className="text-lg font-medium">API Usage</CardTitle>
							<p className="text-sm text-muted-foreground">Total API requests</p>
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{isLoading ? (
									<span className="text-muted-foreground">Loading...</span>
								) : (
									`${stats.apiUsage} requests`
								)}
							</div>
						</CardContent>
					</Card>
				</div>
			) : (
				<div className="space-y-6">
					<Card>
						<CardHeader>
							<CardTitle>Welcome to Your Dashboard</CardTitle>
						</CardHeader>
						<CardContent>
							<p>Welcome to your personal dashboard. You can manage your profile and account settings here.</p>
							<div className="mt-4">
								<Link href="/dashboard/profile">
									<Button>
										<User className="mr-2 h-4 w-4" />
										Manage Profile
									</Button>
								</Link>
							</div>
						</CardContent>
					</Card>
				</div>
			)}
		</div>
	);
}
