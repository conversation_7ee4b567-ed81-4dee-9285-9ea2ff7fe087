import { Suspense, type ReactElement } from 'react';
import Hero from '@/components/Hero';
import FeaturedProjects from '@/components/Project/FeaturedProjects';
import FeaturedProducts from '@/components/Product/FeaturedProducts';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowRight, Code, Gamepad2, Layers } from 'lucide-react';

export const revalidate = 0;

export default function Home(): ReactElement {
	return (
		<main className="flex flex-col gap-10 sm:gap-16 md:gap-20 lg:gap-24 max-w-7xl mx-auto px-4 pb-16">
			{/* Hero Section */}
			<Hero />

			{/* About Section */}
			<section className="py-12 px-4 sm:px-6 lg:px-8 bg-card rounded-xl shadow-sm border border-border/50">
				<div className="max-w-4xl mx-auto text-center">
					<h2 className="text-3xl font-bold mb-8 relative inline-block pb-4 after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:-translate-x-1/2 after:w-16 after:h-1 after:bg-primary after:rounded-full">
						About RealSoft Games
					</h2>
					<p className="text-lg mb-8 text-muted-foreground">
						We create innovative game development tools and assets for Unity developers, focusing on quality, performance, and ease of use.
					</p>
					<div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
						<div className="flex flex-col items-center p-6 rounded-lg bg-background border border-border/50">
							<div className="p-3 rounded-full bg-primary/10 mb-4">
								<Gamepad2 className="h-8 w-8 text-primary" />
							</div>
							<h3 className="text-xl font-semibold mb-2">Game Development</h3>
							<p className="text-center text-muted-foreground">
								Creating engaging games and interactive experiences for various platforms.
							</p>
						</div>
						<div className="flex flex-col items-center p-6 rounded-lg bg-background border border-border/50">
							<div className="p-3 rounded-full bg-primary/10 mb-4">
								<Code className="h-8 w-8 text-primary" />
							</div>
							<h3 className="text-xl font-semibold mb-2">Unity Assets</h3>
							<p className="text-center text-muted-foreground">
								Developing high-quality tools and assets for the Unity Asset Store.
							</p>
						</div>
						<div className="flex flex-col items-center p-6 rounded-lg bg-background border border-border/50">
							<div className="p-3 rounded-full bg-primary/10 mb-4">
								<Layers className="h-8 w-8 text-primary" />
							</div>
							<h3 className="text-xl font-semibold mb-2">Custom Solutions</h3>
							<p className="text-center text-muted-foreground">
								Building tailored solutions for specific game development challenges.
							</p>
						</div>
					</div>
				</div>
			</section>

			{/* Featured Products Section */}
			<section>
				<h2 className="text-4xl font-bold mb-12 text-center relative pb-4 after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:-translate-x-1/2 after:w-24 after:h-1 after:bg-primary after:rounded-full">
					Featured Products
				</h2>
				<Suspense fallback={
					<div className="my-12">
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-items-center">
							{[1, 2, 3].map((i) => (
								<Card key={i} className="flex flex-col w-full max-w-sm relative animate-pulse">
									<CardHeader>
										<div className="h-6 bg-muted rounded w-3/4"></div>
									</CardHeader>
									<CardContent className="flex-grow">
										<div className="relative w-full h-48 mb-4 bg-muted rounded-md"></div>
										<div className="mt-2 mb-3">
											<div className="h-4 bg-muted rounded w-full mb-2"></div>
											<div className="h-4 bg-muted rounded w-5/6"></div>
										</div>
										<div className="h-6 bg-muted rounded w-1/4 mt-2"></div>
									</CardContent>
									<CardFooter>
										<div className="h-10 bg-muted rounded w-full"></div>
									</CardFooter>
								</Card>
							))}
						</div>
					</div>
				}>
					<FeaturedProducts />
				</Suspense>
				<div className="flex justify-center mt-8">
					<Button asChild variant="outline" className="group">
						<Link href="/products" className="flex items-center gap-2">
							View All Products
							<ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
						</Link>
					</Button>
				</div>
			</section>

			{/* Featured Projects Section */}
			<section>
				<h2 className="text-4xl font-bold mb-12 text-center relative pb-4 after:content-[''] after:absolute after:bottom-0 after:left-1/2 after:-translate-x-1/2 after:w-24 after:h-1 after:bg-primary after:rounded-full">
					Featured Projects
				</h2>
				<Suspense fallback={
					<div className="my-12">
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-items-center">
							{[1, 2, 3].map((i) => (
								<Card key={i} className="flex flex-col w-full max-w-sm relative animate-pulse">
									<CardHeader>
										<div className="h-6 bg-muted rounded w-3/4"></div>
									</CardHeader>
									<CardContent className="flex-grow">
										<div className="relative w-full h-48 mb-4 bg-muted rounded-md"></div>
										<div className="mt-2 mb-3">
											<div className="h-4 bg-muted rounded w-full mb-2"></div>
											<div className="h-4 bg-muted rounded w-5/6"></div>
										</div>
										<div className="h-6 bg-muted rounded w-1/4 mt-2"></div>
									</CardContent>
									<CardFooter>
										<div className="h-10 bg-muted rounded w-full"></div>
									</CardFooter>
								</Card>
							))}
						</div>
					</div>
				}>
					<FeaturedProjects />
				</Suspense>
				<div className="flex justify-center mt-8">
					<Button asChild variant="outline" className="group">
						<Link href="/projects" className="flex items-center gap-2">
							View All Projects
							<ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
						</Link>
					</Button>
				</div>
			</section>

			{/* Call to Action Section */}
			<section className="bg-primary/5 rounded-xl p-8 md:p-12 text-center border border-primary/10">
				<h2 className="text-3xl font-bold mb-4">Ready to Collaborate?</h2>
				<p className="text-lg mb-8 max-w-2xl mx-auto text-muted-foreground">
					Whether you're looking for game development assets or custom solutions, we're here to help bring your vision to life.
				</p>
				<Button asChild size="lg" className="rounded-full px-8">
					<Link href="/contact">Get in Touch</Link>
				</Button>
			</section>
		</main>
	);
}
