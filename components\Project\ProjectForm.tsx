'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { IProject } from '@/models/Project';
import {
	IMediaFrontend,
	mediaToFrontend,
	frontendToMedia
} from '@/models/Media';
import { Status } from '@/types/global';
import MediaInput, { MediaInputRef } from '../Carousel/MediaInput';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import dynamic from 'next/dynamic';
import axios from 'axios';
import WebGLDemoSettings from '../Admin/WebGLDemoSettings';

import ReactQuillEditor from '../ui/ReactQuillEditor';
import 'react-quill/dist/quill.snow.css';

interface ProjectFormProps {
	initialData: IProject | null;
	onSubmit: (data: Partial<IProject>) => void;
	onCancel: () => void;
}

interface IMediaFrontendWithFile extends IMediaFrontend {
	file?: File;
}

type FormData = Partial<Omit<IProject, 'media'>> & {
	media: IMediaFrontendWithFile[];
};

const ProjectForm: React.FC<ProjectFormProps> = ({
	initialData,
	onSubmit,
	onCancel
}) => {
	const mediaInputRef = useRef<MediaInputRef>(null);
	const [formData, setFormData] = useState<FormData>(() => {
		if (initialData) {
			return {
				...initialData,
				media: initialData.media.map(mediaToFrontend),
				webglDemo: initialData.webglDemo || {
					enabled: false,
					demoUrl: '',
					mediaId: ''
				}
			};
		}
		return {
			title: '',
			description: '',
			price: '',
			media: [],
			externalLink: '',
			liveDemoLink: '',
			status: Status.Draft,
			isFeatured: false,
			webglDemo: {
				enabled: false,
				demoUrl: '',
				mediaId: ''
			}
		};
	});

	const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
	const [removedMedia, setRemovedMedia] = useState<IMediaFrontendWithFile[]>(
		[]
	);

	const [youtubeUrl, setYoutubeUrl] = useState('');

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		setFormData((prev) => ({ ...prev, [name]: value }));
	};

	const handleDescriptionChange = (content: string) => {
		setFormData((prev) => ({ ...prev, description: content }));
	};

	const handleSwitchChange = (name: string) => (isSelected: boolean) => {
		setFormData((prev) => ({ ...prev, [name]: isSelected }));
	};

	const handleMediaChange = (
		newMediaOrUpdater:
			| IMediaFrontend[]
			| ((prevItems: IMediaFrontend[]) => IMediaFrontend[])
	) => {
		setFormData((prev) => {
			const newMedia =
				typeof newMediaOrUpdater === 'function'
					? newMediaOrUpdater(prev.media)
					: newMediaOrUpdater;

			const newUploadedFiles = newMedia
				.filter(
					(item) =>
						item.isTemp &&
						item.type === 'image' &&
						item.url.startsWith('/uploads/images/')
				)
				.map((item) => item.url);

			setUploadedFiles((prevFiles) => [...prevFiles, ...newUploadedFiles]);

			return { ...prev, media: newMedia };
		});
	};

	const removeMediaItem = (index: number) => {
		setFormData((prev) => {
			const updatedMedia = [...prev.media];
			const removedItem = updatedMedia.splice(index, 1)[0];
			if (!removedItem.isTemp) {
				setRemovedMedia((prev) => [...prev, removedItem]);
			}
			return { ...prev, media: updatedMedia };
		});
	};

	const moveMediaItem = useCallback((dragIndex: number, hoverIndex: number) => {
		setFormData((prevFormData) => ({
			...prevFormData,
			media: update(prevFormData.media || [], {
				$splice: [
					[dragIndex, 1],
					[hoverIndex, 0, (prevFormData.media || [])[dragIndex]]
				]
			})
		}));
	}, []);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		// Process media items (upload new ones, remove deleted ones)
		const updatedMedia = await Promise.all(
			formData.media.map(async (item) => {
				if (item.isTemp && item.file) {
					const formData = new FormData();
					formData.append('file', item.file);

					// Build the URL with query parameters for item ID and type
					let uploadUrl = '/api/upload?type=image';
					if (initialData?._id) {
						uploadUrl += `&itemId=${initialData._id.toString()}&itemType=projects`;
					}

					try {
						const response = await axios.post(uploadUrl, formData, {
							headers: { 'Content-Type': 'multipart/form-data' }
						});
						return { ...item, url: response.data.url, isTemp: false };
					} catch (error) {
						console.error('Error uploading file:', error);
						return item;
					}
				}
				return item;
			})
		);

		// Prepare the final data for submission
		const submissionData: Partial<IProject> = {
			...formData,
			media: updatedMedia.map(frontendToMedia)
		};

		// Call the onSubmit prop with the prepared data
		onSubmit(submissionData);
	};

	const handleCancel = async () => {
		// Clean up any temporary files
		for (const fileUrl of uploadedFiles) {
			const fileName = fileUrl.split('/').pop();
			if (fileName) {
				try {
					await axios.delete(
						`/api/upload?fileName=${encodeURIComponent(fileName)}`
					);
				} catch (error) {
					console.error('Error deleting temporary file:', error);
				}
			}
		}

		// Call the onCancel prop
		onCancel();
	};

	const handleAddYouTubeUrl = (e: React.MouseEvent<HTMLButtonElement>) => {
		e.preventDefault();
		if (youtubeUrl) {
			const videoId = extractYouTubeVideoId(youtubeUrl);
			if (videoId) {
				const newMediaItem: IMediaFrontend = {
					type: 'youtube',
					url: `https://www.youtube.com/embed/${videoId}`,
					status: 'success',
					isTemp: false
				};

				setFormData((prev) => ({
					...prev,
					media: [...prev.media, newMediaItem]
				}));

				setYoutubeUrl('');
			} else {
				console.error('Invalid YouTube URL');
			}
		}
	};

	const extractYouTubeVideoId = (url: string): string | null => {
		const regExp =
			/^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
		const match = url.match(regExp);
		return match && match[2].length === 11 ? match[2] : null;
	};

	const handleYoutubeUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setYoutubeUrl(e.target.value);
	};

	return (
		<DndProvider backend={HTML5Backend}>
			<Card className="w-full max-w-2xl mx-auto">
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle>
						{initialData ? 'Edit Project' : 'Create New Project'}
					</CardTitle>
					<Button
						type="button"
						variant="outline"
						onClick={handleCancel}
						className="bg-secondary hover:bg-secondary/80"
					>
						Back to Projects
					</Button>
				</CardHeader>
				<CardContent>
					<form onSubmit={handleSubmit} className="space-y-6">
						<div className="space-y-2">
							<Label htmlFor="title">Title</Label>
							<Input
								id="title"
								name="title"
								value={formData.title}
								onChange={handleChange}
								required
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="description">Description</Label>
							<ReactQuillEditor
								value={formData.description || ''}
								onChange={handleDescriptionChange}
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="price">Price</Label>
							<Input
								id="price"
								name="price"
								value={formData.price}
								onChange={handleChange}
								required
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="externalLink">External Link</Label>
							<Input
								id="externalLink"
								name="externalLink"
								value={formData.externalLink}
								onChange={handleChange}
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="liveDemoLink">Live Demo Link</Label>
							<Input
								id="liveDemoLink"
								name="liveDemoLink"
								value={formData.liveDemoLink}
								onChange={handleChange}
							/>
						</div>

						<div className="space-y-2">
							<Label htmlFor="media">Media</Label>
							<MediaInput
								ref={mediaInputRef}
								items={formData.media}
								onChange={handleMediaChange}
								onRemove={removeMediaItem}
								moveMediaItem={moveMediaItem}
								itemId={initialData?._id?.toString()}
								itemType="projects"
							/>
						</div>

						<div className="flex items-center space-x-4">
							<Switch
								id="featured-toggle"
								checked={formData.isFeatured}
								onCheckedChange={handleSwitchChange('isFeatured')}
							/>
							<Label htmlFor="featured-toggle" className="flex flex-col">
								<span className="font-semibold">Featured Project</span>
								<span className="text-sm text-muted-foreground">
									{formData.isFeatured ? 'Featured' : 'Not Featured'}
								</span>
							</Label>
						</div>

						<div className="flex items-center space-x-4">
							<Switch
								id="status-toggle"
								checked={formData.status === Status.Published}
								onCheckedChange={(isChecked) =>
									setFormData((prev) => ({
										...prev,
										status: isChecked ? Status.Published : Status.Draft
									}))
								}
							/>
							<Label htmlFor="status-toggle" className="flex flex-col">
								<span className="font-semibold">Project Status</span>
								<span className="text-sm text-muted-foreground">
									{formData.status === Status.Published ? 'Published' : 'Draft'}
								</span>
							</Label>
						</div>

						{initialData?._id && (
							<div className="space-y-2 mt-6 pt-6 border-t">
								<WebGLDemoSettings
									itemId={initialData._id.toString()}
									itemType="projects"
									media={formData.media}
									initialSettings={formData.webglDemo}
									onSave={async (settings) => {
										setFormData(prev => ({
											...prev,
											webglDemo: settings
										}));
									}}
									onDelete={async (mediaId) => {
										try {
											await axios.delete(`/api/webgl/${mediaId}`);
											// Remove the media item from the form data
											setFormData(prev => ({
												...prev,
												media: prev.media.filter(item => item._id !== mediaId),
												webglDemo: {
													...prev.webglDemo,
													enabled: false,
													mediaId: ''
												}
											}));
										} catch (error) {
											console.error('Error deleting WebGL build:', error);
										}
									}}
								/>
							</div>
						)}

						<div className="flex justify-end space-x-2">
							<Button type="button" variant="outline" onClick={handleCancel}>
								Cancel
							</Button>
							<Button type="submit">
								{initialData ? 'Update' : 'Create'} Project
							</Button>
						</div>
					</form>
				</CardContent>
			</Card>
		</DndProvider>
	);
};

export default ProjectForm;
