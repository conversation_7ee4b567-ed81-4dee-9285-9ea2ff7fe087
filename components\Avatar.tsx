import React, { forwardRef, useState } from 'react';
import {
	Avatar as UIAvatar,
	AvatarFallback,
	AvatarImage
} from '@/components/ui/avatar';
import { User } from 'lucide-react';
import { cn } from '@/lib/utils';

interface AvatarProps {
	src?: string | null;
	fallback?: string;
	className?: string;
	size?: 'sm' | 'md' | 'lg';
}

export const Avatar = forwardRef<HTMLSpanElement, AvatarProps>(
	({ src, fallback, className, size = 'md' }, ref) => {
		const [imageError, setImageError] = useState(false);
		const sizeClasses = {
			sm: 'h-8 w-8',
			md: 'h-10 w-10',
			lg: 'h-16 w-16'
		};

		// Add a timestamp to force a fresh load and prevent caching
		const timestamp = Date.now();
		let avatarSrc = src;

		// Handle different URL formats
		if (src) {
			// If it's a relative URL that's not already a proxy URL, add the base URL
			if (src.startsWith('/') && !src.startsWith('/api/avatar-proxy')) {
				avatarSrc = `${process.env.NEXT_PUBLIC_URL || ''}${src}`;
			}

			// If it's a direct MinIO URL, use it directly without proxy
			if (src.includes('minioapi.realsoftgames.com')) {
				// Add timestamp to URL to prevent caching
				const separator = src.includes('?') ? '&' : '?';
				avatarSrc = `${src}${separator}t=${timestamp}`;
			} else if (avatarSrc && !avatarSrc.includes('t=')) {
				// For other URLs, add timestamp if not already present
				const separator = avatarSrc.includes('?') ? '&' : '?';
				avatarSrc = `${avatarSrc}${separator}t=${timestamp}`;
			}
		} else {
			// If no src is provided, set avatarSrc to undefined
			avatarSrc = undefined;
			setImageError(true); // Ensure fallback is shown when no src is provided
		}

		// Reset image error state when src changes
		React.useEffect(() => {
			setImageError(!avatarSrc);
		}, [avatarSrc]);

		// For debugging
		// console.log('Avatar component received src:', src);
		// console.log('Avatar component using avatarSrc:', avatarSrc);
		// console.log('Avatar component image error state:', imageError);

		return (
			<UIAvatar ref={ref} className={cn(sizeClasses[size], className)}>
				<AvatarImage
					src={avatarSrc || undefined}
					alt="User avatar"
					onError={() => setImageError(true)}
					className={imageError ? 'hidden' : undefined}
				/>
				<AvatarFallback
					className={cn(
						"bg-gray-200 dark:bg-gray-700 text-gray-900 dark:text-white",
						!imageError && "hidden"
					)}
				>
					{fallback ? fallback.slice(0, 2).toUpperCase() : <User />}
				</AvatarFallback>
			</UIAvatar>
		);
	}
);

Avatar.displayName = 'Avatar';
