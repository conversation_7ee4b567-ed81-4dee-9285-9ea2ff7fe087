import './globals.css';
import { Inter } from 'next/font/google';
import { Providers } from '@/components/Providers';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ErrorBoundary from '@/components/error-boundary';
import { Toaster } from '@/components/ui/toaster';
import { ThemeInitScript } from './theme-init-script';
import ThemeEffect from './theme-effect';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
	title: 'RealSoft Games',
	description: 'Game Development Portfolio',
	icons: {
		icon: '/favicon.ico',
		shortcut: '/favicon-16x16.png'
	}
};

export default function RootLayout({
	children
}: {
	children: React.ReactNode;
}) {
	return (
		<html lang="en" suppressHydrationWarning>
			<head>
				<ThemeInitScript />
			</head>
			<body className={inter.className}>
				<Providers>
					<ThemeEffect />
					<ErrorBoundary>
						<div className="flex flex-col min-h-screen">
							<Header />
							<main className="flex-grow">{children}</main>
							<Footer />
						</div>
						<Toaster />
					</ErrorBoundary>
				</Providers>
			</body>
		</html>
	);
}
