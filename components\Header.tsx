'use client';

import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import Image from 'next/image';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import SafeAvatar from '@/components/SafeAvatar';
import { useEffect, useState } from 'react';
import { useAvatar } from '@/contexts/AvatarContext';
import { Menu } from 'lucide-react';
import dynamic from 'next/dynamic';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';

const ThemeToggle = dynamic(
	() => import('./ThemeToggle').then((mod) => mod.ThemeToggle),
	{ ssr: false }
) as React.ComponentType;

export default function Header() {
	const { data: session, status } = useSession();
	const { avatarUrl } = useAvatar();
	const [mounted, setMounted] = useState(false);
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

	useEffect(() => {
		setMounted(true);

		// Listen for avatar update events
		const handleAvatarUpdate = () => {
			// Force a re-render of the component
			setMounted(false);
			setTimeout(() => setMounted(true), 0);
		};

		window.addEventListener('avatar-updated', handleAvatarUpdate);

		return () => {
			window.removeEventListener('avatar-updated', handleAvatarUpdate);
		};
	}, []);

	const handleSignOut = () => {
		signOut({ callbackUrl: '/' });
	};

	const isAuthenticated = status === 'authenticated' && session?.user;

	const menuItems = [
		{ href: '/products', label: 'Products' },
		{ href: '/projects', label: 'Projects' },
		{ href: '/contact', label: 'Contact' }
	];

	if (isAuthenticated) {
		menuItems.push({ href: '/dashboard', label: 'Dashboard' });
	}

	const closeMobileMenu = () => {
		setMobileMenuOpen(false);
	};

	if (!mounted) {
		return null;
	}

	return (
		<header className="bg-background text-foreground border-b border-border">
			<div className="container mx-auto px-4 py-4">
				<div className="flex items-center justify-between">
					<Link
						href="/"
						className="flex items-center"
						onClick={closeMobileMenu}
					>
						<div className="relative h-10 w-10">
							<Image
								src="/images/RSG 128x128_Holo.png"
								alt="RealSoft Games Logo"
								fill
								className="object-contain"
							/>
						</div>
						<span className="ml-2 text-xl font-bold hidden sm:inline">RealSoft Games</span>
					</Link>

					<div className="flex items-center space-x-4">
						<nav className="hidden md:flex items-center space-x-6">
							{menuItems.map((item) => (
								<Link
									key={item.href}
									href={item.href}
									className="hover:text-muted-foreground transition-colors font-medium"
								>
									{item.label}
								</Link>
							))}
						</nav>

						{isAuthenticated ? (
							<DropdownMenu>
								<DropdownMenuTrigger>
									<SafeAvatar
										src={avatarUrl}
										alt={session.user.username || 'User avatar'}
										fallback={session.user.username?.[0] || 'U'}
									/>
								</DropdownMenuTrigger>
								<DropdownMenuContent className="bg-background text-foreground border border-border">
									<DropdownMenuItem
										onSelect={handleSignOut}
										className="hover:bg-muted focus:bg-muted"
									>
										Sign Out
									</DropdownMenuItem>
								</DropdownMenuContent>
							</DropdownMenu>
						) : (
							<Link
								href="/api/auth/signin"
								className="hidden md:block hover:text-muted-foreground transition-colors font-medium"
								onClick={closeMobileMenu}
							>
								Sign In
							</Link>
						)}

						<ThemeToggle />

						<button
							className="md:hidden focus:outline-none transition-transform duration-300 ease-in-out"
							onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
						>
							<Menu
								className={`h-6 w-6 transform ${
									mobileMenuOpen ? 'rotate-90' : 'rotate-0'
								}`}
							/>
						</button>
					</div>
				</div>

				{/* Mobile navigation */}
				<nav
					className={`md:hidden mt-4 bg-background text-foreground rounded-lg shadow-lg p-4 transition-all duration-300 ease-in-out ${
						mobileMenuOpen
							? 'max-h-96 opacity-100'
							: 'max-h-0 opacity-0 overflow-hidden'
					}`}
				>
					{menuItems.map((item) => (
						<Link
							key={item.href}
							href={item.href}
							className="block py-2 hover:bg-muted rounded transition-colors"
							onClick={closeMobileMenu}
						>
							{item.label}
						</Link>
					))}
					{isAuthenticated ? (
						<button
							onClick={() => {
								handleSignOut();
								closeMobileMenu();
							}}
							className="block w-full text-left py-2 hover:bg-muted rounded transition-colors"
						>
							Sign Out
						</button>
					) : (
						<Link
							href="/api/auth/signin"
							className="block py-2 hover:bg-muted rounded transition-colors"
							onClick={closeMobileMenu}
						>
							Sign In
						</Link>
					)}
				</nav>
			</div>
		</header>
	);
}
