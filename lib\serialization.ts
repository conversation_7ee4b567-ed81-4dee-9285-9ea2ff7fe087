/**
 * Utility functions for serializing MongoDB objects to plain JavaScript objects
 * to avoid "Objects with toJSON methods are not supported" warnings in Next.js
 */
import { Types } from 'mongoose';

/**
 * Determines if an object is a MongoDB ObjectId
 */
function isObjectId(value: any): boolean {
  return (
    value instanceof Types.ObjectId ||
    (value && value._bsontype === 'ObjectID') ||
    (value && value._bsontype === 'ObjectId') ||
    (value && typeof value === 'object' && value.buffer && typeof value.toString === 'function')
  );
}

/**
 * Serializes a MongoDB document or any object that might contain MongoDB ObjectIds
 * @param obj The object to serialize
 * @returns A plain JavaScript object with all MongoDB-specific types converted to strings
 */
export function serializeData(obj: any): any {
  // If null or undefined, return as is
  if (obj === null || obj === undefined) {
    return obj;
  }

  // If it's a primitive value, return as is
  if (typeof obj !== 'object') {
    return obj;
  }

  // If it's a Date, convert to ISO string
  if (obj instanceof Date) {
    return obj.toISOString();
  }

  // Handle MongoDB ObjectId
  if (isObjectId(obj)) {
    return obj.toString();
  }

  // If it has a toJSON method (like ObjectId), use it but then serialize the result
  if (typeof obj.toJSON === 'function') {
    return serializeData(obj.toJSON());
  }

  // Handle Mongoose document
  if (typeof obj.toObject === 'function') {
    return serializeData(obj.toObject());
  }

  // If it's an array, serialize each item
  if (Array.isArray(obj)) {
    return obj.map(item => serializeData(item));
  }

  // If it's a regular object, serialize each property
  const serialized: Record<string, any> = {};

  for (const [key, value] of Object.entries(obj) as [string, any][]) {
    // Skip the MongoDB internal __v field
    if (key === '__v') continue;

    // Handle _id specially
    if (key === '_id') {
      // Convert _id to string and also add an 'id' field for convenience
      serialized._id = isObjectId(value) ? value.toString() : serializeData(value);
      serialized.id = serialized._id;
      continue;
    }

    // Special handling for media array items
    if (key === 'media' && Array.isArray(value)) {
      serialized[key] = value.map(item => {
        if (item && typeof item === 'object') {
          const serializedItem = {...serializeData(item)};
          return serializedItem;
        }
        return item;
      });
    } else {
      // Recursively serialize all other properties
      serialized[key] = serializeData(value);
    }
  }

  return serialized;
}

/**
 * Serializes an array of MongoDB documents
 * @param docs Array of documents to serialize
 * @returns Array of serialized documents
 */
export function serializeDataArray<T = any>(docs: any[]): T[] {
  return docs.map(doc => serializeData(doc));
}
