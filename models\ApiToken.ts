import mongoose, { Schema, Document } from 'mongoose';
import crypto from 'crypto';

export interface IApiToken extends Document {
  _id: mongoose.Types.ObjectId;
  userId: string;
  name: string;
  token: string;
  lastUsed?: Date;
  usageCount: number;
  createdAt: Date;
  updatedAt: Date;
  generateToken: () => string;
  incrementUsage: () => Promise<void>;
}

const ApiTokenSchema: Schema = new Schema(
  {
    userId: { type: String, required: true },
    name: { type: String, required: true },
    token: { type: String, required: true, unique: true },
    lastUsed: { type: Date },
    usageCount: { type: Number, default: 0 }
  },
  {
    timestamps: true
  }
);

// Method to generate a new token
ApiTokenSchema.methods.generateToken = function(): string {
  const token = crypto.randomBytes(32).toString('hex');
  this.token = token;
  return token;
};

// Method to increment usage count
ApiTokenSchema.methods.incrementUsage = async function(): Promise<void> {
  this.lastUsed = new Date();
  this.usageCount = (this.usageCount || 0) + 1;
  console.log(`Incrementing usage count for token ${this._id} to ${this.usageCount}`);
  await this.save();
};

// Static method to create a new token for a user
ApiTokenSchema.statics.createToken = async function(userId: string, name: string): Promise<IApiToken> {
  const token = crypto.randomBytes(32).toString('hex');
  const apiToken = new this({
    userId,
    name,
    token
  });

  await apiToken.save();
  return apiToken;
};

const ApiToken = mongoose.models.ApiToken || mongoose.model<IApiToken>('ApiToken', ApiTokenSchema);

export default ApiToken;
