import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongoose';
import User from '@/models/User';
import bcrypt from 'bcryptjs';

export async function POST(req: Request) {
	try {
		const { token, password } = await req.json();

		await connectToDatabase();

		const user = await User.findOne({
			resetToken: token,
			resetTokenExpiry: { $gt: Date.now() }
		});

		if (!user) {
			return NextResponse.json(
				{ error: 'Invalid or expired token' },
				{ status: 400 }
			);
		}

		const hashedPassword = await bcrypt.hash(password, 10);

		user.password = hashedPassword;
		user.resetToken = undefined;
		user.resetTokenExpiry = undefined;

		await user.save();

		return NextResponse.json({ message: 'Password reset successfully' });
	} catch (error) {
		console.error('Reset password error:', error);
		return NextResponse.json({ error: 'An error occurred' }, { status: 500 });
	}
}
