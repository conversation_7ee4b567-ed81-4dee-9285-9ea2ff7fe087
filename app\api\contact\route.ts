import { NextResponse } from 'next/server'
import nodemailer from 'nodemailer'
import * as z from 'zod'

// Define validation schema
const contactSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  message: z.string().min(10, { message: 'Message must be at least 10 characters.' })
});

export async function POST(request: Request) {
  try {
    // Parse and validate the request body
    const body = await request.json()

    // Validate the input
    const result = contactSchema.safeParse(body)
    if (!result.success) {
      const errorMessage = result.error.errors.map(err => `${err.path}: ${err.message}`).join(', ')
      return NextResponse.json({ message: `Validation error: ${errorMessage}` }, { status: 400 })
    }

    const { name, email, message } = result.data

    // Check if email configuration exists
    if (!process.env.GMAIL_USER || !process.env.GMAIL_PASS) {
      console.error('Email configuration missing')
      return NextResponse.json({ message: 'Server configuration error' }, { status: 500 })
    }

    // Use GMAIL_USER as both sender and recipient
    const emailUser = process.env.GMAIL_USER

    // Create a transporter using Gmail
    let transporter = nodemailer.createTransport({
      service: 'gmail',
      auth: {
        user: emailUser,
        pass: process.env.GMAIL_PASS,
      },
    })

    // Send email
    let info = await transporter.sendMail({
      from: `"RealSoft Games Contact" <${emailUser}>`,
      replyTo: `"${name}" <${email}>`,
      to: emailUser,
      subject: `New Contact from ${name}`,
      text: `Name: ${name}\nEmail: ${email}\nMessage: ${message}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
          <div style="text-align: center; margin-bottom: 20px;">
            <img src="https://minioapi.realsoftgames.com/realsoftgames/images/RSG%20128x128_Holo.png" alt="RealSoft Games" style="width: 80px; height: auto;">
            <h2 style="color: #333; margin-top: 10px;">New Contact Form Submission</h2>
          </div>

          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px; margin-bottom: 15px;">
            <p style="margin: 0 0 10px 0;"><strong style="color: #555;">Name:</strong> ${name}</p>
            <p style="margin: 0 0 10px 0;"><strong style="color: #555;">Email:</strong> <a href="mailto:${email}" style="color: #0066cc; text-decoration: none;">${email}</a></p>
          </div>

          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 4px;">
            <p style="margin: 0 0 10px 0;"><strong style="color: #555;">Message:</strong></p>
            <div style="white-space: pre-wrap; background-color: white; padding: 10px; border-radius: 3px; border: 1px solid #e0e0e0;">${message.replace(/\n/g, '<br>')}</div>
          </div>

          <div style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e0e0e0; font-size: 12px; color: #777; text-align: center;">
            <p>This email was sent from the contact form on the RealSoft Games website.</p>
          </div>
        </div>
      `,
    })

    console.log("Message sent: %s", info.messageId)
    return NextResponse.json({ message: 'Email sent successfully' }, { status: 200 })
  } catch (error) {
    console.error('Error sending email:', error)

    // Determine if it's a validation error or server error
    const statusCode = error instanceof z.ZodError ? 400 : 500
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred'

    return NextResponse.json({
      message: 'An error occurred while sending the email',
      error: errorMessage
    }, { status: statusCode })
  }
}