import React, {
	useRef,
	useState,
	useEffect,
	forwardRef,
	useImperativeHandle
} from 'react';
import Image from 'next/image';
import MinioImage from '@/components/MinioImage';
import { useDrag, useDrop } from 'react-dnd';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import axios from 'axios';
import { Icon } from '@iconify/react';
import { IMediaFrontend } from '@/models/Media';
import WebGLUpload from './WebGLUpload';
import { Gamepad2, Image as ImageIcon, Youtube } from 'lucide-react';

interface MediaInputProps {
	items: IMediaFrontend[];
	onChange: (
		items:
			| IMediaFrontend[]
			| ((prevItems: IMediaFrontend[]) => IMediaFrontend[])
	) => void;
	onRemove: (index: number) => void;
	moveMediaItem: (dragIndex: number, hoverIndex: number) => void;
	itemId?: string; // ID of the project or product
	itemType?: 'projects' | 'products'; // Type of item
}

export interface MediaInputRef {
	cleanupTempFiles: () => Promise<void>;
}

const MediaInput = forwardRef<MediaInputRef, MediaInputProps>(
	({ items, onChange, onRemove, moveMediaItem, itemId, itemType }, ref) => {
		const fileInputRef = useRef<HTMLInputElement>(null);
		const [youtubeUrl, setYoutubeUrl] = useState('');
		const [activeTab, setActiveTab] = useState('image');

		const [isTouchDevice, setIsTouchDevice] = useState(false);

		useEffect(() => {
			setIsTouchDevice(
				'ontouchstart' in window || navigator.maxTouchPoints > 0
			);
		}, []);

		useImperativeHandle(ref, () => ({
			cleanupTempFiles: async () => {
				const tempItems = items.filter((item) => item.isTemp);
				for (const item of tempItems) {
					if (
						item.type === 'image' &&
						item.url.startsWith('/uploads/images/')
					) {
						const fileName = item.url.split('/').pop();
						if (fileName) {
							try {
								await axios.delete(
									`/api/upload?fileName=${encodeURIComponent(fileName)}`
								);
							} catch (error) {
								console.error('Error deleting temp file:', error);
							}
						}
					}
				}
				onChange(items.filter((item) => !item.isTemp));
			}
		}));

		const uploadFile = async (file: File, index: number) => {
			const formData = new FormData();
			formData.append('file', file);

			// Build the URL with query parameters for item ID and type
			let uploadUrl = '/api/upload?type=image';
			if (itemId && itemType) {
				uploadUrl += `&itemId=${itemId}&itemType=${itemType}`;
			}

			try {
				const response = await axios.post(uploadUrl, formData, {
					headers: {
						'Content-Type': 'multipart/form-data'
					},
					onUploadProgress: (progressEvent) => {
						if (progressEvent.total) {
							const progress = Math.round(
								(progressEvent.loaded * 100) / progressEvent.total
							);
							onChange((prevItems) =>
								prevItems.map((item, i) =>
									i === index
										? { ...item, status: 'uploading', progress }
										: item
								)
							);
						}
					}
				});

				onChange((prevItems) =>
					prevItems.map((item, i) =>
						i === index
							? {
									...item,
									status: 'success',
									progress: 100,
									url: response.data.url
							  }
							: item
					)
				);
				return response.data.url;
			} catch (error) {
				console.error('Error uploading file:', error);
				onChange((prevItems) =>
					prevItems.map((item, i) =>
						i === index ? { ...item, status: 'error' } : item
					)
				);
				throw error;
			}
		};

		const handleChooseFiles = (e: React.MouseEvent<HTMLButtonElement>) => {
			e.preventDefault(); // Prevent form submission
			fileInputRef.current?.click();
		};

		const handleImageUpload = async (
			e: React.ChangeEvent<HTMLInputElement>
		) => {
			const files = e.target.files;
			if (files) {
				const newItems: IMediaFrontend[] = Array.from(files).map((file) => ({
					type: 'image',
					url: URL.createObjectURL(file),
					status: 'uploading',
					progress: 0,
					isTemp: true,
					file: file
				}));

				onChange((prevItems) => [...prevItems, ...newItems]);

				for (let i = 0; i < files.length; i++) {
					const file = files[i];
					try {
						const formData = new FormData();
						formData.append('file', file);

						// Build the URL with query parameters for item ID and type
						let uploadUrl = '/api/upload?type=image';
						if (itemId && itemType) {
							uploadUrl += `&itemId=${itemId}&itemType=${itemType}`;
						}

						const response = await axios.post(uploadUrl, formData, {
							headers: { 'Content-Type': 'multipart/form-data' },
							onUploadProgress: (progressEvent) => {
								if (progressEvent.total) {
									const progress = Math.round(
										(progressEvent.loaded * 100) / progressEvent.total
									);
									onChange((prevItems) =>
										prevItems.map((item, index) =>
											index === prevItems.length - files.length + i
												? { ...item, progress }
												: item
										)
									);
								}
							}
						});
						onChange((prevItems) =>
							prevItems.map((item, index) =>
								index === prevItems.length - files.length + i
									? {
											...item,
											url: response.data.url,
											status: 'success',
											progress: 100,
											isTemp: false
									  }
									: item
							)
						);
					} catch (error) {
						console.error('Error uploading file:', error);
						onChange((prevItems) =>
							prevItems.map((item, index) =>
								index === prevItems.length - files.length + i
									? { ...item, status: 'error' }
									: item
							)
						);
					}
				}
			}
		};

		const handleYouTubeAdd = () => {
			if (youtubeUrl.trim()) {
				onChange([
					...items,
					{ type: 'youtube', url: youtubeUrl.trim(), isTemp: true }
				]);
				setYoutubeUrl('');
			}
		};

		const handleWebGLUpload = (webglMedia: IMediaFrontend) => {
			onChange([...items, webglMedia]);
		};

		const handleRemove = async (index: number) => {
			const item = items[index];
			if (
				item.isTemp &&
				item.type === 'image' &&
				item.url.startsWith('/uploads/images/')
			) {
				const fileName = item.url.split('/').pop();
				if (fileName) {
					try {
						await axios.delete(
							`/api/upload?fileName=${encodeURIComponent(fileName)}`
						);
					} catch (error) {
						console.error('Error deleting file:', error);
					}
				}
			}
			onRemove(index);
		};

		const handleMoveItem = (dragIndex: number, hoverIndex: number) => {
			const dragItem = items[dragIndex];
			const newItems = [...items];
			newItems.splice(dragIndex, 1);
			newItems.splice(hoverIndex, 0, dragItem);
			onChange(newItems);
		};

		return (
			<div className="space-y-4">
				<div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
					{items.map((item, index) => (
						<MediaItem
							key={item._id || index}
							item={item}
							index={index}
							onRemove={handleRemove}
							moveMediaItem={handleMoveItem}
							isTouchDevice={isTouchDevice}
							totalItems={items.length}
						/>
					))}
				</div>
				<Tabs value={activeTab} onValueChange={setActiveTab}>
					<TabsList className="grid w-full grid-cols-3">
						<TabsTrigger value="image" className="flex items-center gap-2">
							<ImageIcon className="h-4 w-4" />
							<span>Images</span>
						</TabsTrigger>
						<TabsTrigger value="youtube" className="flex items-center gap-2">
							<Youtube className="h-4 w-4" />
							<span>YouTube</span>
						</TabsTrigger>
						<TabsTrigger value="webgl" className="flex items-center gap-2">
							<Gamepad2 className="h-4 w-4" />
							<span>WebGL</span>
						</TabsTrigger>
					</TabsList>
					<TabsContent value="image">
						<div className="flex flex-col space-y-2">
							<Button
								onClick={handleChooseFiles}
								variant="outline"
								className="w-full"
								type="button"
							>
								Choose Files
							</Button>
							<input
								type="file"
								ref={fileInputRef}
								className="hidden"
								multiple
								accept="image/*"
								onChange={handleImageUpload}
							/>
							<p className="text-sm text-muted-foreground">
								{items.filter((item) => item.isTemp).length > 0
									? `${
											items.filter((item) => item.isTemp).length
									  } file(s) selected`
									: 'No file chosen'}
							</p>
						</div>
					</TabsContent>
					<TabsContent value="youtube">
						<div className="flex space-x-2">
							<Input
								placeholder="YouTube URL"
								value={youtubeUrl}
								onChange={(e) => setYoutubeUrl(e.target.value)}
								className="flex-grow"
							/>
							<Button onClick={handleYouTubeAdd}>Add YouTube</Button>
						</div>
					</TabsContent>
					<TabsContent value="webgl">
						<WebGLUpload
							onUpload={handleWebGLUpload}
							itemId={itemId}
							itemType={itemType as 'projects' | 'products'}
						/>
					</TabsContent>
				</Tabs>
			</div>
		);
	}
);

MediaInput.displayName = 'MediaInput';

const getYouTubeVideoId = (url: string): string | null => {
	if (!url) return null;

	try {
		// Handle different YouTube URL formats
		if (url.includes('youtu.be/')) {
			// Short URL format: https://youtu.be/VIDEO_ID
			const parts = url.split('youtu.be/');
			if (parts.length > 1) {
				const idPart = parts[1].split(/[?&#]/);
				return idPart[0];
			}
		} else if (url.includes('/embed/')) {
			// Embed URL format: https://www.youtube.com/embed/VIDEO_ID
			const parts = url.split('/embed/');
			if (parts.length > 1) {
				const idPart = parts[1].split(/[?&#]/);
				return idPart[0];
			}
		} else if (url.includes('v=')) {
			// Standard URL format: https://www.youtube.com/watch?v=VIDEO_ID
			const videoParam = new URL(url).searchParams.get('v');
			if (videoParam) return videoParam;
		}

		// Fallback to regex for other formats
		const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=)([^#\&\?]*).*/;
		const match = url.match(regExp);
		return match && match[2].length === 11 ? match[2] : null;
	} catch (error) {
		console.error('Error parsing YouTube URL:', error);
		return null;
	}
};

const MediaItem: React.FC<{
	item: IMediaFrontend;
	index: number;
	onRemove: (index: number) => void;
	moveMediaItem: (dragIndex: number, hoverIndex: number) => void;
	isTouchDevice: boolean;
	totalItems: number;
}> = ({ item, index, onRemove, moveMediaItem, isTouchDevice, totalItems }) => {
	const ref = useRef<HTMLDivElement>(null);

	const [{ isDragging }, drag] = useDrag({
		type: 'mediaItem',
		item: () => ({ id: item._id, index }),
		collect: (monitor) => ({
			isDragging: monitor.isDragging()
		})
	});

	const [, drop] = useDrop({
		accept: 'mediaItem',
		hover(draggedItem: { id: string; index: number }, monitor) {
			if (!ref.current) return;
			const dragIndex = draggedItem.index;
			const hoverIndex = index;
			if (dragIndex === hoverIndex) return;

			const hoverBoundingRect = ref.current.getBoundingClientRect();
			const hoverMiddleY =
				(hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
			const clientOffset = monitor.getClientOffset();
			const hoverClientY = clientOffset!.y - hoverBoundingRect.top;

			if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;
			if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;

			moveMediaItem(dragIndex, hoverIndex);
			draggedItem.index = hoverIndex;
		}
	});

	if (!isTouchDevice) {
		drag(drop(ref));
	}

	// Safely get thumbnail URL
	const thumbnailUrl = (() => {
		if (!item.url) return '/images/placeholder.jpg';

		if (item.type === 'youtube') {
			const videoId = getYouTubeVideoId(item.url);
			return videoId ? `https://img.youtube.com/vi/${videoId}/0.jpg` : '/images/placeholder.jpg';
		}

		if (item.type === 'webgl') {
			// Use background image if available, otherwise use a placeholder
			return item.backgroundUrl || '/images/webgl-placeholder.jpg';
		}

		return item.url;
	})();

	return (
		<div
			ref={ref}
			className={`relative ${
				isDragging ? 'opacity-50' : ''
			} group aspect-square`}
		>
			<div className="aspect-square overflow-hidden rounded-lg relative" style={{ height: '100%' }}>
				<MinioImage
					src={thumbnailUrl}
					alt={
						item.type === 'image'
							? 'Media'
							: item.type === 'youtube'
								? 'YouTube Thumbnail'
								: item.title || 'WebGL Game'
					}
					fill
					style={{ objectFit: 'cover' }}
					className="rounded-lg"
				/>
				{item.status === 'uploading' && (
					<div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
						<span className="text-white font-bold">{item.progress}%</span>
					</div>
				)}
				{item.status === 'error' && (
					<div className="absolute inset-0 bg-red-500 bg-opacity-50 flex items-center justify-center">
						<span className="text-white font-bold">Error</span>
					</div>
				)}
				{/* Media type indicator */}
				<div className="absolute top-1 left-1 bg-black bg-opacity-50 text-white p-1 rounded-md text-xs">
					{item.type === 'image' && <ImageIcon className="h-3 w-3" />}
					{item.type === 'youtube' && <Youtube className="h-3 w-3" />}
					{item.type === 'webgl' && <Gamepad2 className="h-3 w-3" />}
				</div>
			</div>
			<button
				className="absolute top-1 right-1 w-6 h-6 flex items-center justify-center text-white bg-black bg-opacity-50 rounded-full opacity-0 group-hover:opacity-100 transition-opacity z-20"
				onClick={() => onRemove(index)}
			>
				✕
			</button>
			<div className="absolute inset-x-0 bottom-0 flex justify-between p-2 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity">
				<button
					type="button"
					className="text-white"
					onClick={(e) => {
						e.preventDefault();
						moveMediaItem(index, index - 1);
					}}
					disabled={index === 0}
				>
					<Icon icon="mdi:chevron-left" />
				</button>
				<button
					type="button"
					className="text-white"
					onClick={(e) => {
						e.preventDefault();
						moveMediaItem(index, index + 1);
					}}
					disabled={index === totalItems - 1}
				>
					<Icon icon="mdi:chevron-right" />
				</button>
			</div>
		</div>
	);
};

export default MediaInput;
