import { NextRequest, NextResponse } from 'next/server';
import { headers } from 'next/headers';
import sharp from 'sharp';

export const dynamic = 'force-dynamic';

// Cache duration in seconds
const CACHE_DURATION = 2592000; // 30 days

/**
 * Optimized image loading API that intelligently routes requests
 * to the fastest available source and applies image optimization
 */
export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const imageUrl = searchParams.get('url');
    const bucket = searchParams.get('bucket');
    const key = searchParams.get('key');
    const width = searchParams.get('w') ? parseInt(searchParams.get('w') as string, 10) : undefined;
    const quality = searchParams.get('q') ? parseInt(searchParams.get('q') as string, 10) : 80;
    const format = searchParams.get('format') || 'webp';
    const noprocess = searchParams.get('noprocess') === 'true';

    if (!imageUrl && (!bucket || !key)) {
      console.error('Missing required parameters:', { imageUrl, bucket, key });
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Decode URL-encoded parameters
    if (bucket) {
      bucket = decodeURIComponent(bucket);
    }
    if (key) {
      key = decodeURIComponent(key);
    }
    if (imageUrl) {
      imageUrl = decodeURIComponent(imageUrl);
    }

    // Get client IP to determine if they're on the local network
    const headersList = headers();
    const forwardedFor = headersList.get('x-forwarded-for');
    const clientIp = forwardedFor ? forwardedFor.split(',')[0].trim() : '0.0.0.0';

    // Check if client is on local network (192.168.0.x)
    const isLocalNetwork = clientIp.startsWith('192.168.0.');

    // Determine the best URL to fetch from
    let fetchUrl: string;

    if (imageUrl) {
      // If a full URL is provided
      if (isLocalNetwork && (
          imageUrl.includes('minioapi.realsoftgames.com') ||
          imageUrl.startsWith('/minio/')
      )) {
        // Convert public URL to local URL for faster access
        try {
          let pathParts: string[];

          if (imageUrl.includes('minioapi.realsoftgames.com')) {
            const urlObj = new URL(imageUrl);
            pathParts = urlObj.pathname.split('/');
            if (pathParts.length >= 2) {
              const bucket = pathParts[1];
              const key = pathParts.slice(2).join('/');
              fetchUrl = `http://************:9000/${bucket}/${key}`;
            } else {
              fetchUrl = imageUrl;
            }
          } else if (imageUrl.startsWith('/minio/')) {
            pathParts = imageUrl.split('/');
            if (pathParts.length >= 3) {
              const bucket = pathParts[2];
              const key = pathParts.slice(3).join('/');
              fetchUrl = `http://************:9000/${bucket}/${key}`;
            } else {
              fetchUrl = imageUrl;
            }
          } else {
            fetchUrl = imageUrl;
          }
        } catch (err) {
          console.error('Error parsing URL:', err);
          fetchUrl = imageUrl;
        }
      } else {
        // Use the provided URL as is
        fetchUrl = imageUrl;
      }
    } else if (bucket && key) {
      // If bucket and key are provided directly
      fetchUrl = isLocalNetwork
        ? `http://************:9000/${bucket}/${key}`
        : `https://minioapi.realsoftgames.com/${bucket}/${key}`;
    } else {
      return NextResponse.json(
        { error: 'Invalid parameters' },
        { status: 400 }
      );
    }

    // Fetch the image
    const response = await fetch(fetchUrl, {
      headers: {
        'Accept': 'image/*',
      },
      next: {
        revalidate: 3600 // Cache for 1 hour on the server
      }
    });

    if (!response.ok) {
      console.error(`Failed to fetch image from ${fetchUrl}: ${response.status} ${response.statusText}`);

      // If local fetch fails, try the public URL as fallback
      if (isLocalNetwork && fetchUrl.includes('************')) {
        console.log('Local fetch failed, trying public URL...');
        const publicUrl = fetchUrl.replace(
          'http://************:9000',
          'https://minioapi.realsoftgames.com'
        );

        const fallbackResponse = await fetch(publicUrl, {
          headers: {
            'Accept': 'image/*',
          },
          next: {
            revalidate: 3600
          }
        });

        if (!fallbackResponse.ok) {
          return NextResponse.json(
            { error: 'Failed to fetch image from both local and public URLs' },
            { status: 502 }
          );
        }

        const imageBuffer = await fallbackResponse.arrayBuffer();

        // If noprocess is true, just pass through the image without processing
        if (noprocess) {
          // Get the content type from the response
          const contentType = fallbackResponse.headers.get('Content-Type') || 'image/jpeg';

          // Return the image as-is with aggressive caching headers
          return new NextResponse(Buffer.from(imageBuffer), {
            headers: {
              'Content-Type': contentType,
              'Cache-Control': `public, max-age=${CACHE_DURATION}, immutable`,
              'Content-Length': Buffer.from(imageBuffer).length.toString(),
              'Access-Control-Allow-Origin': '*',
            },
          });
        }

        return processAndOptimizeImage(imageBuffer, width, quality, format);
      }

      return NextResponse.json(
        { error: 'Failed to fetch image' },
        { status: 502 }
      );
    }

    const imageBuffer = await response.arrayBuffer();

    // If noprocess is true, just pass through the image without processing
    if (noprocess) {
      // Get the content type from the response
      const contentType = response.headers.get('Content-Type') || 'image/jpeg';

      // Return the image as-is with aggressive caching headers
      return new NextResponse(Buffer.from(imageBuffer), {
        headers: {
          'Content-Type': contentType,
          'Cache-Control': `public, max-age=${CACHE_DURATION}, immutable`,
          'Content-Length': Buffer.from(imageBuffer).length.toString(),
          'Access-Control-Allow-Origin': '*',
        },
      });
    }

    // Otherwise, process and optimize the image
    return processAndOptimizeImage(imageBuffer, width, quality, format);
  } catch (error) {
    console.error('Error in optimized-image API:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * Process and optimize the image using Sharp
 */
async function processAndOptimizeImage(
  imageBuffer: ArrayBuffer,
  width?: number,
  quality: number = 80,
  format: string = 'webp'
): Promise<NextResponse> {
  try {
    let sharpInstance = sharp(Buffer.from(imageBuffer));

    // Get image metadata
    const metadata = await sharpInstance.metadata();

    // Resize if width is specified and it's smaller than the original
    if (width && metadata.width && width < metadata.width) {
      sharpInstance = sharpInstance.resize(width);
    }

    // Convert to the requested format with quality settings
    let processedImageBuffer: Buffer;
    let contentType: string;

    switch (format.toLowerCase()) {
      case 'webp':
        processedImageBuffer = await sharpInstance.webp({ quality }).toBuffer();
        contentType = 'image/webp';
        break;
      case 'avif':
        processedImageBuffer = await sharpInstance.avif({ quality }).toBuffer();
        contentType = 'image/avif';
        break;
      case 'jpeg':
      case 'jpg':
        processedImageBuffer = await sharpInstance.jpeg({ quality }).toBuffer();
        contentType = 'image/jpeg';
        break;
      case 'png':
        processedImageBuffer = await sharpInstance.png({ quality }).toBuffer();
        contentType = 'image/png';
        break;
      default:
        // Default to WebP for best compression/quality ratio
        processedImageBuffer = await sharpInstance.webp({ quality }).toBuffer();
        contentType = 'image/webp';
    }

    // Return the optimized image with appropriate headers
    return new NextResponse(processedImageBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': `public, max-age=${CACHE_DURATION}, stale-while-revalidate=86400`,
        'Content-Length': processedImageBuffer.length.toString(),
        'Vary': 'Accept',
      },
    });
  } catch (error) {
    console.error('Error optimizing image:', error);

    // Return the original image if optimization fails
    return new NextResponse(Buffer.from(imageBuffer), {
      headers: {
        'Content-Type': 'image/jpeg', // Fallback content type
        'Cache-Control': `public, max-age=${CACHE_DURATION}`,
        'Content-Length': Buffer.from(imageBuffer).length.toString(),
      },
    });
  }
}
