import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongoose';
import User from '@/models/User';
import { UserRole } from '@/types/global';
import mongoose from 'mongoose';
import { cookies, headers } from 'next/headers';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Explicitly await cookies and headers to avoid warnings
    const cookiesList = await cookies();
    const headersList = await headers();

    // Await authOptions to properly handle cookies and headers
    const options = await authOptions();
    const session = await getServerSession(options);
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    await connectToDatabase();
    const { id } = await params;

    const user = await User.findById(id, '-password').lean();
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Format the user data
    const formattedUser = {
      ...user,
      id: user._id.toString(),
      _id: user._id.toString()
    };

    return NextResponse.json(formattedUser);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Explicitly await cookies and headers to avoid warnings
    const cookiesList = await cookies();
    const headersList = await headers();

    // Await authOptions to properly handle cookies and headers
    const options = await authOptions();
    const session = await getServerSession(options);
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    await connectToDatabase();
    const { id } = await params;
    const data = await request.json();

    console.log('Updating user with data:', data);

    // Validate role if it's being updated
    if (data.role && !Object.values(UserRole).includes(data.role)) {
      return NextResponse.json(
        { error: 'Invalid role value' },
        { status: 400 }
      );
    }

    // Find and update the user
    const user = await User.findByIdAndUpdate(
      id,
      {
        ...data
      },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    console.log('Updated user:', user);

    // Format the user data
    const formattedUser = {
      ...user.toObject(),
      id: user._id.toString(),
      _id: user._id.toString()
    };

    return NextResponse.json(formattedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Explicitly await cookies and headers to avoid warnings
    const cookiesList = await cookies();
    const headersList = await headers();

    // Await authOptions to properly handle cookies and headers
    const options = await authOptions();
    const session = await getServerSession(options);
    if (!session || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    await connectToDatabase();
    const { id } = await params;

    // Prevent deleting your own account
    if (id === session.user.id) {
      return NextResponse.json(
        { error: 'You cannot delete your own account' },
        { status: 400 }
      );
    }

    const user = await User.findByIdAndDelete(id);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'User deleted successfully' });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Failed to delete user' },
      { status: 500 }
    );
  }
}
