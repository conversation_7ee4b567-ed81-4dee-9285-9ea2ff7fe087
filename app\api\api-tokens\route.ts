import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongoose';
import ApiToken from '@/models/ApiToken';
import { UserRole } from '@/types/global';
import { serializeData } from '@/lib/serialization';
import crypto from 'crypto';

export const dynamic = 'force-dynamic';

// Get all API tokens for the current user
export async function GET(req: Request) {
  try {
    const session = await getServerSession(await authOptions());
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await connectToDatabase();

    const tokens = await ApiToken.find({ userId: session.user.id }).lean();

    // Don't return the actual token values for security
    const safeTokens = tokens.map(token => ({
      ...token,
      token: undefined
    }));

    return NextResponse.json(serializeData(safeTokens));
  } catch (error) {
    console.error('Error fetching API tokens:', error);
    return NextResponse.json(
      { error: 'Failed to fetch API tokens' },
      { status: 500 }
    );
  }
}

// Create a new API token
export async function POST(req: Request) {
  try {
    // Await authOptions to properly handle cookies and headers
    const options = await authOptions();
    const session = await getServerSession(options);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can create API tokens
    if (session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    await connectToDatabase();

    const { name } = await req.json();

    if (!name) {
      return NextResponse.json(
        { error: 'Token name is required' },
        { status: 400 }
      );
    }

    // Generate a new token
    const token = crypto.randomBytes(32).toString('hex');

    // Create the token
    const apiToken = new ApiToken({
      userId: session.user.id,
      name,
      token
    });

    await apiToken.save();

    // Return the full token only when it's first created
    return NextResponse.json(
      {
        _id: apiToken._id.toString(),
        name: apiToken.name,
        token: apiToken.token,
        createdAt: apiToken.createdAt
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating API token:', error);
    return NextResponse.json(
      { error: 'Failed to create API token' },
      { status: 500 }
    );
  }
}
