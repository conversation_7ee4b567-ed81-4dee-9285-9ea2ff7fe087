/**
 * Utility functions for handling MongoDB data in Next.js
 */

import { Types } from 'mongoose';

/**
 * Serializes MongoDB documents to plain JavaScript objects
 * Converts ObjectIds to strings and handles nested objects/arrays
 */
export function serializeDocument<T>(doc: any): T {
  if (!doc) {
    return doc;
  }

  if (Array.isArray(doc)) {
    return doc.map(item => serializeDocument(item)) as any;
  }

  if (doc instanceof Date) {
    return doc.toISOString() as any;
  }

  if (doc instanceof Types.ObjectId) {
    return doc.toString() as any;
  }

  if (doc instanceof Object && doc.constructor === Object) {
    const serialized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(doc)) {
      // Skip the MongoDB internal __v field
      if (key === '__v') continue;
      
      // Handle _id specially
      if (key === '_id') {
        serialized.id = value instanceof Types.ObjectId ? value.toString() : value;
      }
      
      serialized[key] = serializeDocument(value);
    }
    
    return serialized as T;
  }

  return doc;
}

/**
 * Serializes MongoDB documents and converts _id to id
 */
export function serializeDocuments<T>(docs: any[]): T[] {
  return docs.map(doc => serializeDocument<T>(doc));
}
