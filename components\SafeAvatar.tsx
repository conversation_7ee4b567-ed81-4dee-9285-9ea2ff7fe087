'use client';

import { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
// Import the convertToDirectUrl function only on the client side
const convertToDirectUrl = (url: string): string => {
  // Simple client-side implementation
  if (!url) return url;

  // If it's already a direct MinIO URL, return it as is
  if (url.includes('minioapi.realsoftgames.com')) {
    return url;
  }

  // If it's a /minio/ URL, convert it to the direct MinIO URL
  if (url.startsWith('/minio/')) {
    const pathParts = url.split('/');

    if (pathParts.length >= 3) {
      const bucket = pathParts[2];
      const key = pathParts.slice(3).join('/');
      return `https://minioapi.realsoftgames.com/${bucket}/${key}`;
    }
  }

  return url;
};


interface SafeAvatarProps {
  src?: string | null;
  alt?: string;
  fallback?: string;
  className?: string;
}

export default function SafeAvatar({
  src,
  alt,
  fallback,
  className
}: SafeAvatarProps) {
  const [imageSrc, setImageSrc] = useState<string | undefined>(undefined);
  const [isMounted, setIsMounted] = useState(false);

  // Process the source URL on the client side only
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Update image source when src changes
  useEffect(() => {
    if (!src) {
      setImageSrc(undefined);
      return;
    }

    // If src is already a proxy URL, use it directly
    if (src.startsWith('/api/avatar-proxy')) {
      setImageSrc(src);
      return;
    }

    try {
      // Convert to direct URL if needed
      const directUrl = src.includes('minio') ? convertToDirectUrl(src) : src;

      // Create a proxy URL with timestamp for cache busting
      const timestamp = Date.now();
      const proxyUrl = `/api/avatar-proxy?url=${encodeURIComponent(directUrl)}&t=${timestamp}`;

      setImageSrc(proxyUrl);
    } catch (error) {
      console.error('Error processing avatar URL:', error);
      setImageSrc(undefined);
    }
  }, [src]);

  // Determine fallback content
  const fallbackContent = fallback || (alt ? alt.charAt(0).toUpperCase() : '?');

  return (
    <Avatar className={className}>
      {isMounted && imageSrc && (
        <AvatarImage
          src={imageSrc}
          alt={alt || 'Avatar'}
          onError={() => {
            setImageSrc(undefined);
          }}
        />
      )}
      <AvatarFallback>{fallbackContent}</AvatarFallback>
    </Avatar>
  );
}
