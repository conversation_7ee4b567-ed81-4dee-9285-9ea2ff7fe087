'use client';

import { useState, useEffect, useCallback } from 'react';
import ErrorBoundary from '@/components/error-boundary';
import { useSession } from 'next-auth/react';
import { UserRole } from '@/types/global';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { AlertCircle, Copy, Key, RefreshCw, Trash2 } from 'lucide-react';
import { format } from 'date-fns';
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from '@/components/ui/alert';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface ApiToken {
  _id: string;
  name: string;
  token?: string;
  lastUsed?: string;
  usageCount?: number;
  createdAt: string;
  updatedAt: string;
}

export default function ApiTokensPage() {
  const { data: session } = useSession();
  const { toast } = useToast();
  const [tokens, setTokens] = useState<ApiToken[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [newTokenName, setNewTokenName] = useState('');
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newToken, setNewToken] = useState<ApiToken | null>(null);
  // This state is used in the future for regenerating tokens
  // eslint-disable-next-line no-unused-vars
  const [_isRegenerateDialogOpen, _setIsRegenerateDialogOpen] = useState(false);
  const [tokenToRegenerate, setTokenToRegenerate] = useState<ApiToken | null>(null);

  const isAdmin = session?.user?.role === UserRole.ADMIN;

  // State for tracking fetch errors and retry attempts
  const [fetchError, setFetchError] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const MAX_RETRIES = 3;

  // Fetch tokens
  const fetchTokens = useCallback(async (retry = 0) => {
    // Create a flag to track if component is mounted
    const mountedRef = { current: true };

    // Only set loading on first attempt to avoid flashing
    if (retry === 0) {
      setIsLoading(true);
      setFetchError(false);
    }

    try {
      // Add a timeout to the fetch request to prevent hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const response = await fetch('/api/api-tokens', {
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to fetch API tokens: ${response.status}`);
      }

      const data = await response.json();
      if (mountedRef.current) {
        setTokens(data);
        setFetchError(false);
        setRetryCount(0);
      }
    } catch (error) {
      console.error('Error fetching tokens:', error);

      if (mountedRef.current) {
        // Stop retrying after MAX_RETRIES
        if (retry >= MAX_RETRIES) {
          setFetchError(true);
          setIsLoading(false);
          // Only show toast on final retry
          toast({
            title: 'Error',
            description: 'Failed to load API tokens. The API endpoint may not be available.',
            type: 'error',
          });
        } else if (retry < MAX_RETRIES) {
          // Retry with exponential backoff
          const retryDelay = Math.min(1000 * Math.pow(2, retry), 10000);
          const timeoutId = setTimeout(() => {
            if (mountedRef.current) {
              fetchTokens(retry + 1);
            }
          }, retryDelay);

          // Clean up timeout if component unmounts
          return () => {
            clearTimeout(timeoutId);
            mountedRef.current = false;
          };
        }
      }
    } finally {
      if (mountedRef.current && (retry === 0 || retry >= MAX_RETRIES)) {
        setIsLoading(false);
      }
    }

    // Cleanup function
    return () => {
      mountedRef.current = false;
    };
  }, [toast, MAX_RETRIES]);


  useEffect(() => {
    let cleanup = () => {};
    let isMounted = true;

    if (session) {
      // Only fetch tokens if we haven't reached the maximum retry count
      if (retryCount < MAX_RETRIES || fetchError) {
        try {
          // Call fetchTokens and store the cleanup function
          const cleanupFn = fetchTokens();
          if (typeof cleanupFn === 'function') {
            cleanup = cleanupFn;
          } else if (cleanupFn instanceof Promise) {
            cleanupFn.then(fn => {
              if (isMounted && typeof fn === 'function') {
                cleanup = fn;
              }
            }).catch(err => {
              // Silently handle promise rejection
              console.error('Error in fetchTokens cleanup promise:', err);
            });
          }
        } catch (error) {
          // Handle any synchronous errors
          console.error('Error in fetchTokens effect:', error);
          if (isMounted) {
            setFetchError(true);
            setIsLoading(false);
          }
        }
      }
    }

    // Return cleanup function
    return () => {
      isMounted = false;
      cleanup();
    };
  }, [session, fetchTokens, retryCount, fetchError, MAX_RETRIES]);

  // Reset retry count when component unmounts
  useEffect(() => {
    return () => {
      setRetryCount(0);
      setFetchError(false);
    };
  }, []);

  // Create a new token
  const handleCreateToken = async () => {
    if (!newTokenName.trim()) {
      toast({
        title: 'Error',
        description: 'Token name is required',
        type: 'error',
      });
      return;
    }

    try {
      const response = await fetch('/api/api-tokens', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: newTokenName }),
      });

      if (!response.ok) {
        throw new Error('Failed to create API token');
      }

      const data = await response.json();
      setIsCreateDialogOpen(false);
      setNewToken(data);
      setNewTokenName('');

      // Add the new token to the list (without the token value for security)
      const newTokenForList = {
        _id: data._id,
        name: data.name,
        createdAt: data.createdAt,
        updatedAt: data.createdAt,
        usageCount: 0
      };

      setTokens(prevTokens => [...prevTokens, newTokenForList]);
    } catch (error) {
      console.error('Error creating token:', error);
      toast({
        title: 'Error',
        description: 'Failed to create API token',
        type: 'error',
      });
    }
  };

  // Regenerate a token
  const handleRegenerateToken = async (tokenId: string) => {
    try {
      const response = await fetch(`/api/api-tokens/${tokenId}`, {
        method: 'PUT',
      });

      if (!response.ok) {
        throw new Error('Failed to regenerate API token');
      }

      const data = await response.json();
      setTokenToRegenerate(data);

      // Update the token in the list (without the token value for security)
      setTokens(prevTokens =>
        prevTokens.map(token =>
          token._id === tokenId
            ? { ...token, updatedAt: data.updatedAt }
            : token
        )
      );
    } catch (error) {
      console.error('Error regenerating token:', error);
      toast({
        title: 'Error',
        description: 'Failed to regenerate API token',
        type: 'error',
      });
    }
  };

  // Delete a token
  const handleDeleteToken = async (tokenId: string) => {
    try {
      const response = await fetch(`/api/api-tokens/${tokenId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete API token');
      }

      toast({
        title: 'Success',
        description: 'API token deleted successfully',
      });

      // Remove the token from the list
      setTokens(prevTokens => prevTokens.filter(token => token._id !== tokenId));
    } catch (error) {
      console.error('Error deleting token:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete API token',
        type: 'error',
      });
    }
  };

  // Copy token to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copied',
      description: 'Token copied to clipboard',
    });
  };

  if (!session) {
    return <div>Loading...</div>;
  }

  if (!isAdmin) {
    return (
      <div className="space-y-6">
        <h1 className="text-2xl font-bold">API Tokens</h1>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            You do not have permission to access this page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">API Tokens</h1>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Key className="mr-2 h-4 w-4" />
              Create New Token
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create API Token</DialogTitle>
              <DialogDescription>
                Create a new API token to access your data programmatically.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Token Name</Label>
                <Input
                  id="name"
                  placeholder="Enter a name for your token"
                  value={newTokenName}
                  onChange={(e) => setNewTokenName(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleCreateToken}>Create Token</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Display new token after creation */}
        {newToken && (
          <Dialog
            open={!!newToken}
            onOpenChange={(open) => {
              if (!open) setNewToken(null);
            }}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>API Token Created</DialogTitle>
                <DialogDescription>
                  Your new API token has been created. Please copy it now as you won't be able to see it again.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>Token Name</Label>
                  <div className="font-medium">{newToken.name}</div>
                </div>
                <div className="space-y-2">
                  <Label>API Token</Label>
                  <div className="flex items-center space-x-2">
                    <div className="bg-muted p-2 rounded w-full overflow-x-auto break-all">
                      <code>{newToken.token}</code>
                    </div>
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={() => newToken.token && copyToClipboard(newToken.token)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Important</AlertTitle>
                  <AlertDescription>
                    This token will only be displayed once. If you lose it, you'll need to generate a new one.
                  </AlertDescription>
                </Alert>
              </div>
              <DialogFooter>
                <Button onClick={() => setNewToken(null)}>Done</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}

        {/* Display regenerated token */}
        {tokenToRegenerate && (
          <Dialog
            open={!!tokenToRegenerate}
            onOpenChange={(open) => {
              if (!open) setTokenToRegenerate(null);
            }}
          >
            <DialogContent>
              <DialogHeader>
                <DialogTitle>API Token Regenerated</DialogTitle>
                <DialogDescription>
                  Your API token has been regenerated. Please copy it now as you won't be able to see it again.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>Token Name</Label>
                  <div className="font-medium">{tokenToRegenerate.name}</div>
                </div>
                <div className="space-y-2">
                  <Label>New API Token</Label>
                  <div className="flex items-center space-x-2">
                    <div className="bg-muted p-2 rounded w-full overflow-x-auto break-all">
                      <code>{tokenToRegenerate.token}</code>
                    </div>
                    <Button
                      size="icon"
                      variant="outline"
                      onClick={() => tokenToRegenerate.token && copyToClipboard(tokenToRegenerate.token)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>Important</AlertTitle>
                  <AlertDescription>
                    This token will only be displayed once. If you lose it, you'll need to generate a new one.
                  </AlertDescription>
                </Alert>
              </div>
              <DialogFooter>
                <Button onClick={() => setTokenToRegenerate(null)}>Done</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>API Tokens</CardTitle>
          <CardDescription>
            Manage your API tokens to access the API programmatically.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Last Used</TableHead>
                <TableHead>Usage Count</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center">
                    <div className="py-8 flex justify-center items-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
                      <span className="ml-2">Loading tokens...</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : fetchError ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center">
                    <div className="py-8 flex flex-col items-center">
                      <AlertCircle className="h-8 w-8 text-destructive mb-2" />
                      <div className="text-destructive font-medium">Failed to load API tokens</div>
                      <p className="text-muted-foreground mt-1">There was an error connecting to the server.</p>
                      <Button
                        variant="outline"
                        className="mt-4"
                        onClick={() => fetchTokens()}
                      >
                        <RefreshCw className="mr-2 h-4 w-4" />
                        Retry
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : tokens.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} className="text-center">
                    <div className="py-8">
                      No API tokens found. Create one to get started.
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                tokens.map((token) => (
                  <TableRow key={token._id}>
                    <TableCell className="font-medium">{token.name}</TableCell>
                    <TableCell>
                      {format(new Date(token.createdAt), 'MMM d, yyyy')}
                    </TableCell>
                    <TableCell>
                      {token.lastUsed
                        ? format(new Date(token.lastUsed), 'MMM d, yyyy HH:mm')
                        : 'Never used'}
                    </TableCell>
                    <TableCell>
                      {token.usageCount || 0}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="outline" size="icon">
                              <RefreshCw className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Regenerate API Token</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to regenerate this token? The current token will be invalidated and any applications using it will stop working.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleRegenerateToken(token._id)}
                              >
                                Regenerate
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>

                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button variant="destructive" size="icon">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete API Token</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete this token? This action cannot be undone and any applications using this token will stop working.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteToken(token._id)}
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
        <CardFooter>
          <div className="text-sm text-muted-foreground">
            API tokens are used to authenticate requests to the API. Keep your tokens secure and never share them publicly.
          </div>
        </CardFooter>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>API Usage</CardTitle>
          <CardDescription>
            Learn how to use your API tokens to access the API.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Authentication</h3>
              <p className="text-sm text-muted-foreground mt-1">
                Include your API token in the Authorization header of your requests.
              </p>
              <code className="block bg-muted p-2 rounded mt-2">
                Authorization: Bearer YOUR_API_TOKEN
              </code>
            </div>

            <div>
              <h3 className="text-lg font-medium">Endpoints</h3>
              <div className="mt-2 space-y-4">
                <div>
                  <h4 className="font-medium">Get All Products</h4>
                  <code className="block bg-muted p-2 rounded mt-1">
                    GET /api/public/products
                  </code>
                  <p className="text-sm text-muted-foreground mt-1">
                    Returns all published products.
                  </p>
                </div>

                <div>
                  <h4 className="font-medium">Filter Products</h4>
                  <code className="block bg-muted p-2 rounded mt-1">
                    GET /api/public/products?featured=true
                  </code>
                  <p className="text-sm text-muted-foreground mt-1">
                    Returns only featured products.
                  </p>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-medium">Example Request</h3>
              <code className="block bg-muted p-2 rounded mt-2 whitespace-pre overflow-x-auto">
{`fetch('https://realsoftgames.com/api/public/products', {
  headers: {
    'Authorization': 'Bearer YOUR_API_TOKEN'
  }
})
.then(response => response.json())
.then(data => console.log(data));`}
              </code>
            </div>

            <div className="mt-6">
              <Link href="/dashboard/api-docs">
                <Button>
                  View Full API Documentation
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
    </ErrorBoundary>
  );
}
