import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import Project from '@/models/Project';
import Product from '@/models/Product';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated and is an admin
    if (!session || !session.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const body = await req.json();
    const { itemId, itemType, settings } = body;
    
    if (!itemId || !itemType || !settings) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Validate itemType
    if (itemType !== 'projects' && itemType !== 'products') {
      return NextResponse.json(
        { error: 'Invalid item type' },
        { status: 400 }
      );
    }
    
    // Connect to the database
    await connectToDatabase();
    
    // Update the project or product
    if (itemType === 'projects') {
      const project = await Project.findById(itemId);
      
      if (!project) {
        return NextResponse.json(
          { error: 'Project not found' },
          { status: 404 }
        );
      }
      
      // Update WebGL demo settings
      project.webglDemo = {
        enabled: settings.enabled,
        demoUrl: settings.demoUrl || '',
        mediaId: settings.mediaId || ''
      };
      
      await project.save();
    } else {
      const product = await Product.findById(itemId);
      
      if (!product) {
        return NextResponse.json(
          { error: 'Product not found' },
          { status: 404 }
        );
      }
      
      // Update WebGL demo settings
      product.webglDemo = {
        enabled: settings.enabled,
        demoUrl: settings.demoUrl || '',
        mediaId: settings.mediaId || ''
      };
      
      await product.save();
    }
    
    return NextResponse.json(
      { success: true, message: 'WebGL demo settings updated successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error updating WebGL demo settings:', error);
    return NextResponse.json(
      { error: 'Failed to update WebGL demo settings' },
      { status: 500 }
    );
  }
}
