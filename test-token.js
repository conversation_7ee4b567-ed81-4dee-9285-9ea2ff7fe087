// Simple Node.js script to test API token
// For Node.js v18 or later, use the built-in fetch
// For earlier versions, use the import below
import fetch from 'node-fetch';

// Use the token provided by the user
const API_TOKEN = '321f37d68f91dc9951c368f732566f452a19bd7d7657c857e7b02bdc8c1c0367';
const BASE_URL = 'http://localhost:3000';

async function testApiToken() {
  try {
    console.log('Testing API token...');

    // Test the API token endpoint
    const response = await fetch(`${BASE_URL}/api/test-token`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`
      }
    });

    const data = await response.json();
    console.log('API response:', data);

    if (response.ok) {
      console.log('API token test successful!');
    } else {
      console.error('API request failed with status', response.status);
    }
  } catch (error) {
    console.error('Error testing API token:', error);
  }
}

testApiToken();
