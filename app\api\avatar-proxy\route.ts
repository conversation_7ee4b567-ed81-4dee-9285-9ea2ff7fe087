import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { convertToDirectUrl } from '@/lib/minio';
import { withCors } from '@/app/api/cors-middleware';

// Helper function to proxy an image from a URL
async function proxyImage(url: string) {
  try {
    // Clean up the URL to prevent double timestamps
    let cleanUrl = url;
    if (url.includes('t=')) {
      // Extract the base URL without any timestamp parameters
      const urlObj = new URL(url);
      urlObj.searchParams.delete('t');
      cleanUrl = urlObj.toString();
    }

    // Add a timestamp to the URL to prevent caching
    const timestamp = Date.now();
    const separator = cleanUrl.includes('?') ? '&' : '?';
    const urlWithTimestamp = `${cleanUrl}${separator}t=${timestamp}`;

    console.log('Avatar Proxy: Fetching from:', urlWithTimestamp);

    try {
      const response = await fetch(urlWithTimestamp, {
        // Add a cache: 'no-store' to avoid caching issues during development
        cache: 'no-store'
      });

      return await handleResponse(response);
    } catch (error) {
      console.error('Avatar Proxy: Network error fetching image:', error);
      return new NextResponse('Error fetching image', { status: 404 });
    }
  } catch (error) {
    console.error('Avatar Proxy: Error proxying image:', error);
    // Return a 404 instead of throwing an error
    return new NextResponse('Error fetching image', { status: 404 });
  }
}

// Helper function to handle the response
async function handleResponse(response: Response) {
  try {

    if (!response.ok) {
      console.error(`Avatar Proxy: Failed to fetch image: ${response.status}`);
      // Return a default avatar instead of throwing an error
      return new NextResponse('Image not found', { status: 404 });
    }

    // Get the image data
    const imageBuffer = await response.arrayBuffer();

    // Get the content type
    const contentType = response.headers.get('content-type') || 'image/png';

    // Return the image with the correct content type and no caching
    return new NextResponse(imageBuffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
        'Surrogate-Control': 'no-store',
        'Access-Control-Allow-Origin': '*'
      }
    });
  } catch (error) {
    console.error('Avatar Proxy: Error handling response:', error);
    // Return a 404 instead of throwing an error
    return new NextResponse('Error processing image', { status: 404 });
  }
}

async function handler(request: Request | NextRequest) {
  try {
    // Get the URL from the query parameter
    const url = new URL(request.url).searchParams.get('url');

    if (!url) {
      return new NextResponse('Missing URL parameter', { status: 400 });
    }

    // Decode the URL if it's encoded
    const decodedUrl = decodeURIComponent(url);

    try {
      // If the URL is already a direct MinIO URL, use it directly
      if (decodedUrl.includes('minioapi.realsoftgames.com')) {
        return await proxyImage(decodedUrl);
      }

      // Convert to direct URL and proxy
      const directUrl = convertToDirectUrl(decodedUrl);
      return await proxyImage(directUrl);
    } catch (error) {
      console.error('Avatar Proxy: Error processing URL:', error);
      return new NextResponse('Error processing image URL', { status: 404 });
    }
  } catch (error) {
    console.error('Avatar Proxy: Error processing request:', error);
    return new NextResponse('Error proxying image', { status: 404 });
  }
}

// Apply CORS middleware to the handler
export const GET = (req: NextRequest) => withCors(req, handler);

// Handle OPTIONS requests for CORS
export const OPTIONS = (req: NextRequest) => withCors(req, async () => new NextResponse(null, { status: 204 }));