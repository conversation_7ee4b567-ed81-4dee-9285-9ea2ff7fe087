'use client';

import React, { Component, ErrorInfo, ReactNode, createRef } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  /**
   * Optional name to identify which component triggered the error
   */
  componentName?: string;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

/**
 * A comprehensive error boundary component that catches JavaScript errors
 * in its child component tree and displays a fallback UI.
 */
class ErrorBoundary extends Component<Props, State> {
  private rootRef = createRef<HTMLDivElement>();

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null
    };
  }

  static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // Log the error to console
    console.error(`Error caught by ErrorBoundary${this.props.componentName ? ` in ${this.props.componentName}` : ''}:`, error);
    console.error('Component stack:', errorInfo.componentStack);

    // Here you could also send the error to an error reporting service
    // Example: reportError(error, errorInfo);
  }

  render(): ReactNode {
    if (this.state.hasError) {
      // If a custom fallback is provided, use it
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Detect specific error types
      const isWebSocketError =
        this.state.error?.message?.includes('WebSocket') ||
        this.state.error?.stack?.includes('WebSocket') ||
        this.state.error?.message?.includes('Failed to fetch');

      const isModuleError =
        this.state.error?.message?.includes('Cannot find module') ||
        this.state.error?.message?.includes('MODULE_NOT_FOUND');

      // Choose appropriate alert variant and title based on error type
      const alertVariant = isWebSocketError ? "default" : "destructive";
      const alertTitle =
        isWebSocketError ? "Connection Error" :
        isModuleError ? "Module Loading Error" :
        "Something went wrong";

      return (
        <div className="p-4" ref={this.rootRef}>
          <Alert variant={alertVariant as "default" | "destructive"}>
            <AlertTitle>{alertTitle}</AlertTitle>
            <AlertDescription>
              <div className="mb-4">
                {isWebSocketError ? (
                  <span>There was a problem with the development server connection. This is likely a temporary issue.</span>
                ) : isModuleError ? (
                  <>
                    <div className="mb-2">There was a problem loading a required module. This might be due to:</div>
                    <ul className="list-disc ml-6 mb-4">
                      <li>Incomplete or corrupted installation</li>
                      <li>Missing dependencies</li>
                      <li>Incompatible module versions</li>
                    </ul>
                  </>
                ) : (
                  <span>
                    {this.state.error?.message || 'An unexpected error occurred'}
                    {this.props.componentName && ` in ${this.props.componentName}`}
                  </span>
                )}
              </div>

              <div className="flex flex-wrap gap-3 mt-4">
                <Button
                  onClick={() => this.setState({ hasError: false, error: null })}
                  size="sm"
                >
                  Try Again
                </Button>

                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                  size="sm"
                >
                  Reload Page
                </Button>

                {isModuleError && (
                  <Button
                    onClick={() => window.location.href = '/'}
                    variant="secondary"
                    size="sm"
                  >
                    Go to Homepage
                  </Button>
                )}
              </div>
            </AlertDescription>
          </Alert>
        </div>
      );
    }

    return <div ref={this.rootRef}>{this.props.children}</div>;
  }
}

export default ErrorBoundary;
