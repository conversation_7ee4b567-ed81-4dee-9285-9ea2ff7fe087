import React from 'react';

export default function ProductsLoading() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="w-48 h-8 bg-muted rounded-md animate-pulse mb-8"></div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 6 }).map((_, index) => (
          <div key={index} className="bg-muted rounded-lg overflow-hidden animate-pulse">
            <div className="h-48 bg-muted-foreground/20"></div>
            <div className="p-4 space-y-3">
              <div className="h-6 bg-muted-foreground/20 rounded w-3/4"></div>
              <div className="h-4 bg-muted-foreground/20 rounded w-full"></div>
              <div className="h-4 bg-muted-foreground/20 rounded w-2/3"></div>
              <div className="h-8 bg-muted-foreground/20 rounded w-1/3 mt-4"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
