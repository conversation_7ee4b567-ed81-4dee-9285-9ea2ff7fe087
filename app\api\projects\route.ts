import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongoose';
import Project from '@/models/Project';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { serializeData } from '@/lib/serialization';
import { cookies, headers } from 'next/headers';

export async function GET(req: Request) {
	try {
		// Explicitly await cookies and headers to avoid warnings
		const cookiesList = await cookies();
		const headersList = await headers();

		await connectToDatabase();

		const { searchParams } = new URL(req.url);
		const featured = searchParams.get('featured');

		let query = {};
		if (featured === 'true') {
			query = { isFeatured: true };
		}

		const projects = await Project.find(query).sort({ order: 1 }).lean();

		// Serialize the projects to avoid MongoDB object issues
		const serializedProjects = serializeData(projects);

		return NextResponse.json(serializedProjects, {
			headers: {
				'Cache-Control': 'no-store, max-age=0'
			}
		});
	} catch (error) {
		console.error('Error in GET /api/projects:', error);
		return NextResponse.json(
			{
				error: 'Internal Server Error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
}

export async function POST(req: Request) {
	try {
		// Explicitly await cookies and headers to avoid warnings
		const cookiesList = await cookies();
		const headersList = await headers();

		await connectToDatabase();

		// Await authOptions to properly handle cookies and headers
		const options = await authOptions();
		const session = await getServerSession(options);
		if (!session || !session.user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const data = await req.json();

		const project = new Project({
			...data,
			userId: session.user.id,
			liveDemoLink: data.liveDemoLink // Ensure liveDemoLink is included
		});

		await project.save();

		// Serialize the project to avoid MongoDB object issues
		const serializedProject = serializeData(project.toObject());

		return NextResponse.json(serializedProject, { status: 201 });
	} catch (error: unknown) {
		console.error('Error in POST /api/projects:', error);
		let errorMessage = 'Failed to create project';
		if (error instanceof Error) {
			errorMessage += `: ${error.message}`;
		}
		return NextResponse.json({ error: errorMessage }, { status: 500 });
	}
}
