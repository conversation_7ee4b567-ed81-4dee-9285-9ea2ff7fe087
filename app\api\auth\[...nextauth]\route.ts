import NextAuth from 'next-auth';
import { authOptions } from '@/lib/auth';
import { cookies, headers } from 'next/headers';

// Create an async handler to await authOptions
const createHandler = async () => {
  // Explicitly await cookies and headers to avoid warnings
  const cookiesList = await cookies();
  const headersList = await headers();

  // Await authOptions to properly handle cookies and headers
  const options = await authOptions();
  return NextAuth(options);
};

// Export async functions that create the handler on each request
export async function GET(req: Request, res: Response) {
  const handler = await createHandler();
  return handler(req, res);
}

export async function POST(req: Request, res: Response) {
  const handler = await createHandler();
  return handler(req, res);
}
