version: '3.9'

services:
  nextjs-app:
    image: ************:6500/realsoftgamesportfolio:latest
    container_name: realsoftgamesportfolio
    restart: unless-stopped
    ports:
      - "3060:3000"
    environment:
      # Application Environment
      - NODE_ENV=production
      # Use the actual domain or IP where the site is accessible
      - NEXT_PUBLIC_APP_URL=http://************:3060
      - NEXTAUTH_URL=http://************:3060
      - NEXTAUTH_SECRET=735088a14692bcfb1d9aac09e51c0b7f136e461e5d9de4fc88e7627859f71baa
      
      # Database Configuration - Make sure this is accessible from the container
      - 'MONGODB_URI=mongodb://krazor:M5!vseries@************:27017/gamedevportfolio?authSource=admin&directConnection=true'
      
      # Email Configuration
      - SMTP_HOST=smtp.gmail.com
      - SMTP_PORT=587
      - SMTP_USER=<EMAIL>
      - SMTP_PASSWORD=qtig zstm lqxm nfxt
      - SMTP_FROM=<EMAIL>
      
      # MinIO Configuration - Ensure these are accessible from the container
      - MINIO_ENDPOINT=************
      - MINIO_PORT=9000
      - MINIO_USE_SSL=false
      - MINIO_PUBLIC_URL=https://minioapi.realsoftgames.com
      - MINIO_ACCESS_KEY=N3S84xv2d6SkXyEY5Yo5
      - MINIO_SECRET_KEY=UKRst5kp2msbdzE1txz3eXK162Lc2r62Ni5Sm2GQ
      - MINIO_BUCKET_NAME=realsoftgames
      - MINIO_REGION=us-east-1
      - MINIO_PATH_STYLE=true
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - bridge
    volumes:
      - realsoftgames_logs:/logs

networks:
  bridge:
    driver: bridge

volumes:
  realsoftgames_logs:
