import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { convertToDirectUrl } from '@/lib/minio';
import { cookies } from 'next/headers';

export async function GET(req: Request) {
  try {
    // Explicitly await cookies to avoid the sync dynamic APIs warning
    const cookieStore = await cookies();

    // Get auth options
    const options = await authOptions();

    // Get the session
    const session = await getServerSession(options);

    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the avatar URL from the session
    const avatarUrl = session.user.avatar;

    if (!avatarUrl) {
      return NextResponse.json({ error: 'No avatar found' }, { status: 404 });
    }

    // Convert to direct URL if needed
    const directUrl = convertToDirectUrl(avatarUrl);

    // Return the direct URL
    return NextResponse.json({
      avatarUrl: directUrl,
      originalUrl: avatarUrl
    });
  } catch (error) {
    console.error('Error fetching avatar:', error);
    return NextResponse.json(
      { error: 'Failed to fetch avatar' },
      { status: 500 }
    );
  }
}
