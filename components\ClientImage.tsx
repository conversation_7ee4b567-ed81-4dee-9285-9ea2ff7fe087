'use client';

import React, { useState, useEffect } from 'react';

interface ClientImageProps {
  src: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  priority?: boolean;
}

const ClientImage: React.FC<ClientImageProps> = ({
  src,
  alt,
  className,
  style,
  priority = false
}) => {
  const [error, setError] = useState(false);
  const [loading, setLoading] = useState(true);
  const [imageSrc, setImageSrc] = useState('');

  // Process the image URL with minimal proxying for better performance
  useEffect(() => {
    // Use direct URLs whenever possible for better performance
    if (!src) {
      setImageSrc('');
      return;
    }

    // For MinIO URLs, use them directly without proxying when possible
    if (src.includes('minioapi.realsoftgames.com')) {
      // Use the direct URL without any cache busting for better caching
      setImageSrc(src);
    } else if (src.includes('192.168.0.59')) {
      // For local network URLs, convert to public URLs for external access
      const publicUrl = src.replace('http://192.168.0.59:9000', 'https://minioapi.realsoftgames.com');
      setImageSrc(publicUrl);
    } else if (src.startsWith('/minio/')) {
      // For /minio/ paths, convert to direct MinIO URLs
      const pathParts = src.split('/');
      if (pathParts.length >= 3) {
        const bucket = pathParts[2];
        const key = pathParts.slice(3).join('/');
        setImageSrc(`https://minioapi.realsoftgames.com/${bucket}/${key}`);
      } else {
        setImageSrc(src);
      }
    } else {
      // For non-MinIO images, use the original URL
      setImageSrc(src);
    }
  }, [src]);

  const handleError = (e: React.SyntheticEvent<HTMLImageElement, Event>) => {
    // If direct MinIO URL fails, try the optimized image API as a last resort
    if (imageSrc.includes('minioapi.realsoftgames.com')) {
      try {
        // Extract bucket and key from the URL
        const urlObj = new URL(imageSrc);
        const pathParts = urlObj.pathname.split('/');

        if (pathParts.length >= 2) {
          const bucket = pathParts[1];
          const key = pathParts.slice(2).join('/');

          // Use the optimized image API as a fallback
          const optimizedUrl = `/api/optimized-image?bucket=${encodeURIComponent(bucket)}&key=${encodeURIComponent(key)}`;
          setImageSrc(optimizedUrl);
          return; // Don't set error yet, give the optimized API a chance
        }
      } catch (err) {
        // If parsing fails, continue to error state
        console.error('Error parsing MinIO URL:', err);
      }
    }

    // If we get here, we've either tried all alternatives or it's not a MinIO URL
    setError(true);
    setLoading(false);
  };

  const handleLoad = () => {
    setLoading(false);
  };

  if (error) {
    return (
      <div className={`flex items-center justify-center h-full w-full bg-gray-200 dark:bg-gray-700 rounded-md ${className}`} style={style}>
        <span className="text-gray-500 dark:text-gray-400">Image not available</span>
      </div>
    );
  }

  return (
    <div className="relative w-full h-full overflow-hidden">
      {loading && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse rounded-md" />
      )}
      <img
        src={imageSrc}
        alt={alt}
        className={`${className} transition-opacity duration-300 ${loading ? 'opacity-0' : 'opacity-100'}`}
        style={style}
        onError={handleError}
        onLoad={handleLoad}
        loading={priority ? "eager" : "lazy"}
      />
    </div>
  );
};

export default ClientImage;
