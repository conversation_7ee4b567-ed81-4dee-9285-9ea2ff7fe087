import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { getToken } from "next-auth/jwt"

export async function middleware(request: Request & { nextUrl: URL }) {
  const token = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET })

  if (request.nextUrl.pathname.startsWith('/dashboard')) {
    // Redirect to login if not authenticated
    if (!token) {
      return NextResponse.redirect(new URL('/login', request.url))
    }

    // Define admin-only paths
    const adminOnlyPaths = [
      '/dashboard/users',
      '/dashboard/projects',
      '/dashboard/products',
      '/dashboard/api-tokens'
    ]

    // Check if the current path is an admin-only path
    const isAdminOnlyPath = adminOnlyPaths.some(path =>
      request.nextUrl.pathname.startsWith(path)
    )

    // Redirect non-admin users trying to access admin-only paths
    if (token.role !== 'admin' && isAdminOnlyPath) {
      console.log(`Non-admin user attempting to access ${request.nextUrl.pathname}. Redirecting to dashboard.`)
      return NextResponse.redirect(new URL('/dashboard', request.url))
    }
  }

  return NextResponse.next()
}

export const config = {
  matcher: ['/dashboard/:path*'],
}
