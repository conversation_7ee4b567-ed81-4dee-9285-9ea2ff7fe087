import mongoose, { Schema, Document } from 'mongoose';
import { UserRole } from '@/types/global';

export interface IUser extends Document {
	_id: mongoose.Types.ObjectId;
	username: string;
	name: string;
	email: string;
	password: string;
	dateOfBirth: string;
	role: UserRole;
	resetToken?: string;
	resetTokenExpiry?: Date;
	country?: string;
	avatar?: string;
}

const UserSchema: Schema = new Schema(
	{
		username: { type: String, required: true, unique: true },
		name: { type: String, required: true },
		email: { type: String, required: true, unique: true },
		password: { type: String, required: true },
		dateOfBirth: { type: String, required: true },
		role: {
			type: String,
			required: true,
			enum: Object.values(UserRole),
			default: UserRole.USER
		},
		resetToken: String,
		resetTokenExpiry: Date,
		country: String,
		avatar: String
	},
	{ timestamps: true }
);

const UserModel =
	mongoose.models.User || mongoose.model<IUser>('User', UserSchema, 'users');

export default UserModel;

export type User = Omit<
	IUser,
	'password' | 'resetToken' | 'resetTokenExpiry'
> & {
	id: string;
};
