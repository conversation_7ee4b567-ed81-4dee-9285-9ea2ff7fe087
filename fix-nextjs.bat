@echo off
echo Fixing Next.js installation...

echo Clearing Next.js cache...
if exist .next (
    rmdir /s /q .next
    echo Next.js cache cleared.
) else (
    echo No .next directory found. Skipping cache clear.
)

echo Removing node_modules...
if exist node_modules (
    rmdir /s /q node_modules
    echo node_modules removed.
) else (
    echo No node_modules directory found. Skipping removal.
)

echo Clearing npm cache...
call npm cache clean --force
echo npm cache cleared.

echo Reinstalling dependencies...
call npm install
echo Dependencies reinstalled.

echo Reinstalling specific Next.js version...
call npm install next@13.4.12 --save
echo Next.js reinstalled.

echo Fix completed!
echo You can now run 'npm run dev' to start the development server.
