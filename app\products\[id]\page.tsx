'use client';

import { Suspense, useState, useEffect } from 'react';
import { notFound, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
	CardFooter
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import MediaCarousel from '@/components/Carousel/MediaCarousel';
import { IProduct } from '@/models/Product';
import { IMedia } from '@/models/Media';
// Using API route instead of direct database access
import { Badge } from '@/components/ui/badge';
import WebGLDemo from '@/components/WebGLDemo';
import 'react-quill/dist/quill.snow.css';

// Skeleton loader component
const ProductSkeleton = () => (
	<div className="animate-pulse">
		<div className="h-8 bg-muted rounded w-3/4 mx-auto mb-4"></div>
		<Card className="w-full max-w-4xl mx-auto relative">
			<CardHeader>
				<div className="h-6 bg-muted rounded w-1/2 mx-auto"></div>
			</CardHeader>
			<CardContent className="space-y-6">
				<div className="w-full aspect-video bg-muted rounded-md"></div>
				<div className="flex justify-between items-center">
					<div className="flex space-x-2">
						<div className="h-10 bg-muted rounded w-24"></div>
						<div className="h-10 bg-muted rounded w-24"></div>
					</div>
					<div className="h-10 bg-muted rounded-full w-20"></div>
				</div>
				<div className="space-y-2">
					<div className="h-4 bg-muted rounded w-full"></div>
					<div className="h-4 bg-muted rounded w-5/6"></div>
					<div className="h-4 bg-muted rounded w-4/6"></div>
					<div className="h-4 bg-muted rounded w-full"></div>
					<div className="h-4 bg-muted rounded w-3/4"></div>
				</div>
			</CardContent>
			<CardFooter className="justify-end">
				<div className="h-10 bg-muted rounded w-36"></div>
			</CardFooter>
		</Card>
	</div>
);

// This function is no longer used since we're fetching on the client side

// Client-side component to display product content
function ProductContent({ product }: { product: IProduct }) {
	const [isMounted, setIsMounted] = useState(false);

	useEffect(() => {
		setIsMounted(true);
	}, []);

	const carouselItems = product.media.map((item) => ({
		type: item.type,
		url: item.url
	}));

	const formatPrice = (price: string) => {
		const numPrice = parseFloat(price);
		return numPrice === 0 ? 'Free' : `$${numPrice.toFixed(2)}`;
	};

	return (
		<Card className="w-full max-w-4xl mx-auto relative">
			{product.isFeatured && (
				<div className="absolute top-0 right-0 bg-yellow-400 text-yellow-900 text-xs font-bold px-2 py-1 rounded-bl-md z-10">
					Featured
				</div>
			)}
			<CardHeader className="text-center">
				<CardTitle className="text-3xl">{product.title}</CardTitle>
			</CardHeader>
			<CardContent className="space-y-6">
				{product.media && product.media.length > 0 && (
					<div className="w-full aspect-video">
						<MediaCarousel items={carouselItems} />
					</div>
				)}
				<div className="flex justify-between items-center">
					<div className="flex space-x-2">
						{product.externalLink && (
							<Button asChild size="sm">
								<a
									href={product.externalLink}
									target="_blank"
									rel="noopener noreferrer"
								>
									Visit Product
								</a>
							</Button>
						)}
						{product.liveDemoLink && (
							<Button asChild variant="outline" size="sm">
								<a
									href={product.liveDemoLink}
									target="_blank"
									rel="noopener noreferrer"
								>
									Live Demo
								</a>
							</Button>
						)}
					</div>
					<div className="bg-primary text-primary-foreground rounded-full px-4 py-2 text-lg font-bold">
						{formatPrice(product.price)}
					</div>
				</div>
				{/* WebGL Demo */}
				{product.webglDemo?.enabled && product.webglDemo.mediaId && (
					<div className="mb-6">
						{product.media.map((media: IMedia) => {
							if (media._id === product.webglDemo?.mediaId) {
								return (
									<WebGLDemo
										key={media._id}
										media={media}
										demoUrl={product.webglDemo.demoUrl}
									/>
								);
							}
							return null;
						})}
					</div>
				)}

				{isMounted ? (
					<div
						className="prose max-w-none dark:prose-invert prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-ul:text-foreground prose-ol:text-foreground ql-editor"
						dangerouslySetInnerHTML={{ __html: product.description }}
					/>
				) : (
					<div className="prose max-w-none dark:prose-invert h-40 bg-muted animate-pulse rounded"></div>
				)}
			</CardContent>
			<CardFooter className="justify-end">
				<Button variant="secondary" asChild>
					<Link href="/products">Back to Products</Link>
				</Button>
			</CardFooter>
		</Card>
	);
}

export default function ProductDetailPage({
	params
}: {
	params: Promise<{ id: string }>;
}) {
	const [product, setProduct] = useState<IProduct | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(false);
	const router = useRouter();

	useEffect(() => {
		// Store the ID in a local variable to avoid the dependency issue
		const getProductId = async () => {
			const { id } = await params;
			return id;
		};

		let productId: string;

		async function loadProduct() {
			try {
				setLoading(true);
				productId = await getProductId();
				const response = await fetch(`/api/products/${productId}`);
				if (!response.ok) {
					throw new Error('Failed to fetch product');
				}
				const data = await response.json();
				setProduct(data);
			} catch (err) {
				console.error('Error loading product:', err);
				setError(true);
			} finally {
				setLoading(false);
			}
		}

		loadProduct();
		// eslint-disable-next-line react-hooks/exhaustive-deps
	}, []);

	if (error) {
		return (
			<div className="container mx-auto px-4 py-8 text-center">
				<h2 className="text-2xl font-bold mb-4">Product not found</h2>
				<Button onClick={() => router.push('/products')}>Back to Products</Button>
			</div>
		);
	}

	return (
		<div className="container mx-auto px-4 py-8">
			{loading ? (
				<ProductSkeleton />
			) : product ? (
				<ProductContent product={product} />
			) : (
				<div className="text-center">
					<h2 className="text-2xl font-bold mb-4">Product not found</h2>
					<Button onClick={() => router.push('/products')}>Back to Products</Button>
				</div>
			)}
		</div>
	);
}
