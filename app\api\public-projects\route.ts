import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongoose';
import Project from '@/models/Project';
import { serializeData } from '@/lib/serialization';
import { withCors } from '@/app/api/cors-middleware';

export const dynamic = 'force-dynamic';

/**
 * Public API endpoint for projects that doesn't require authentication
 */
async function handler(req: NextRequest) {
  try {
    console.log('GET /api/public-projects: Starting...');
    await connectToDatabase();
    console.log('Database connected');

    const { searchParams } = new URL(req.url);
    const featured = searchParams.get('featured');

    let query = {};
    if (featured === 'true') {
      query = { isFeatured: true };
    }

    console.log('Query:', query);

    const projects = await Project.find(query).sort({ order: 1 }).lean();
    console.log(`Found ${projects.length} projects`);

    // Serialize the projects to avoid MongoDB object issues
    const serializedProjects = serializeData(projects);

    return NextResponse.json(serializedProjects);
  } catch (error) {
    console.error('Error in GET /api/public-projects:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// Apply CORS middleware to the handler
export const GET = (req: NextRequest) => withCors(req, handler);

// Handle OPTIONS requests for CORS
export const OPTIONS = (req: NextRequest) => withCors(req, async () => new NextResponse(null, { status: 204 }));
