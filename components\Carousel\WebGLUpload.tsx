'use client';

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { IMediaFrontend } from '@/models/Media';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import axios from 'axios';
import { AlertCircle, FileQuestion, HelpCircle, Info, Upload, X } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Checkbox } from "@/components/ui/checkbox";

interface WebGLUploadProps {
  onUpload: (media: IMediaFrontend) => void;
  itemId?: string;
  itemType?: 'projects' | 'products';
}

export default function WebGLUpload({ onUpload, itemId, itemType }: WebGLUploadProps) {
  const [activeTab, setActiveTab] = useState('instructions');
  const [title, setTitle] = useState('');
  // Set fixed dimensions for WebGL builds (16:9 aspect ratio)
  const width = 960;
  const height = 540;
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [demoUrl, setDemoUrl] = useState('');
  const [acceptedTerms, setAcceptedTerms] = useState(false);
  const [zipFile, setZipFile] = useState<File | null>(null);
  const zipFileRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, setFile: React.Dispatch<React.SetStateAction<File | null>>) => {
    if (e.target.files && e.target.files.length > 0) {
      setFile(e.target.files[0]);
    }
  };

  const clearFile = (setFile: React.Dispatch<React.SetStateAction<File | null>>, inputRef: React.RefObject<HTMLInputElement>) => {
    setFile(null);
    if (inputRef.current) {
      inputRef.current.value = '';
    }
  };

  const validateFiles = () => {
    // Validate required fields
    if (!title) {
      setError('Title is required');
      return false;
    }

    if (!acceptedTerms) {
      setError('You must accept the terms to upload a WebGL build');
      return false;
    }

    if (!zipFile) {
      setError('ZIP file is required');
      return false;
    }

    // Validate ZIP file extension
    if (!zipFile.name.toLowerCase().endsWith('.zip')) {
      setError('File must be a ZIP archive');
      return false;
    }

    return true;
  };

  const handleZipUpload = async () => {
    if (!validateFiles()) return;

    setIsUploading(true);
    setError(null);
    setUploadProgress(0);

    try {
      console.log('Starting WebGL ZIP upload...');

      // Test MinIO connection first
      console.log('Testing MinIO connection...');
      try {
        const response = await axios.get('/api/test-minio');
        console.log('MinIO connection test result:', response.data);

        if (!response.data.isConnected) {
          throw new Error('Failed to connect to MinIO storage server. Please try again later.');
        }
      } catch (error) {
        console.error('MinIO connection test failed:', error);
        throw new Error('Failed to connect to MinIO storage server. Please try again later.');
      }

      // Create a folder name for the WebGL build
      const webglFolderName = `${Date.now()}_${title.replace(/\s+/g, '_')}`;

      console.log(`Using folder name: ${webglFolderName}`);
      console.log(`ZIP file: ${zipFile?.name}, size: ${zipFile?.size} bytes`);

      const formData = new FormData();
      formData.append('file', zipFile!);
      formData.append('title', title);
      formData.append('width', width.toString());
      formData.append('height', height.toString());
      formData.append('demoUrl', demoUrl || '');

      // Build the URL with query parameters
      let uploadUrl = `/api/upload-webgl-zip?folder=${webglFolderName}`;
      if (itemId && itemType) {
        uploadUrl += `&itemId=${itemId}&itemType=${itemType}`;
      }

      console.log(`Uploading to: ${uploadUrl}`);

      const response = await axios.post(uploadUrl, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 100));
          setUploadProgress(percentCompleted);
          console.log(`Upload progress: ${percentCompleted}%`);
        }
      });

      // Create the WebGL media item from the response
      const webglMedia: IMediaFrontend = {
        type: 'webgl',
        ...response.data,
        status: 'success',
        demoUrl
      };

      setUploadProgress(100);
      onUpload(webglMedia);

      // Reset form
      resetForm();
    } catch (error) {
      console.error('Error uploading WebGL ZIP:', error);

      // Extract detailed error message if available
      let errorMessage = 'Failed to upload WebGL ZIP. Please try again.';

      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        if (axiosError.response?.data?.error) {
          errorMessage = axiosError.response.data.error;

          // Add details if available
          if (axiosError.response.data.details) {
            errorMessage += `: ${axiosError.response.data.details}`;
          }
        }
      }

      console.error('Error message:', errorMessage);
      setError(errorMessage);
    } finally {
      setIsUploading(false);
    }
  };

  const handleUpload = async () => {
    await handleZipUpload();
  };

  const resetForm = () => {
    setTitle('');
    setDemoUrl('');
    setZipFile(null);
    setAcceptedTerms(false);

    if (zipFileRef.current) zipFileRef.current.value = '';
  };

  return (
    <div className="space-y-4">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="instructions" className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            <span>Instructions</span>
          </TabsTrigger>
          <TabsTrigger value="upload" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            <span>Upload</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="instructions" className="space-y-4">
          <Alert>
            <HelpCircle className="h-4 w-4" />
            <AlertTitle>How to Upload Your Unity WebGL Build</AlertTitle>
            <AlertDescription>
              Follow these steps to upload your Unity WebGL build to our platform.
            </AlertDescription>
          </Alert>

          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="unity-export">
              <AccordionTrigger>Step 1: Export from Unity</AccordionTrigger>
              <AccordionContent>
                <ol className="list-decimal pl-5 space-y-2">
                  <li>In Unity, go to <strong>File &gt; Build Settings</strong></li>
                  <li>Select <strong>WebGL</strong> as the platform</li>
                  <li>Click <strong>Player Settings</strong> and configure:
                    <ul className="list-disc pl-5 mt-1">
                      <li>Set <strong>Compression Format</strong> to <strong>Disabled</strong> for best compatibility</li>
                      <li>Under <strong>Publishing Settings</strong>, enable <strong>Decompression Fallback</strong></li>
                    </ul>
                  </li>
                  <li>Click <strong>Build</strong> and save your build to a folder</li>
                </ol>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="upload-options">
              <AccordionTrigger>Step 2: Prepare Your ZIP File</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-4">
                  <div className="border p-3 rounded-md">
                    <h4 className="font-medium">ZIP Upload Instructions</h4>
                    <p className="text-sm text-muted-foreground mb-2">
                      Compress your entire WebGL build folder into a ZIP file and upload it in one go.
                      Our server will extract and process all the necessary files.
                    </p>
                    <ol className="list-decimal pl-5 text-sm space-y-1">
                      <li>Locate your WebGL build folder (created when you built your game in Unity)</li>
                      <li>Right-click the folder and select "Compress" or "Send to > Compressed (zipped) folder"</li>
                      <li>Make sure the ZIP file contains all the build files at the root level</li>
                      <li>Upload the ZIP file using the form on the next tab</li>
                    </ol>
                  </div>

                  <div className="bg-muted p-3 rounded-md">
                    <h4 className="font-medium">Important Note</h4>
                    <p className="text-sm text-muted-foreground">
                      Your ZIP file should contain these files at the root level:
                    </p>
                    <ul className="list-disc pl-5 mt-1 text-sm">
                      <li><strong>index.html</strong></li>
                      <li><strong>Build/[YourBuildName].loader.js</strong></li>
                      <li><strong>Build/[YourBuildName].data</strong></li>
                      <li><strong>Build/[YourBuildName].framework.js</strong></li>
                      <li><strong>Build/[YourBuildName].wasm</strong></li>
                      <li>And any other files generated by Unity</li>
                    </ul>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="after-upload">
              <AccordionTrigger>Step 3: After Uploading</AccordionTrigger>
              <AccordionContent>
                <p className="mb-2">Once your WebGL build is uploaded:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>It will appear in your project/product media gallery</li>
                  <li>You can share the demo link with others</li>
                  <li>The game will be playable directly on your project/product page</li>
                  <li>You can delete the build later if needed</li>
                </ul>
              </AccordionContent>
            </AccordionItem>

            <AccordionItem value="troubleshooting">
              <AccordionTrigger>Troubleshooting</AccordionTrigger>
              <AccordionContent>
                <p className="mb-2">Common issues and solutions:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li><strong>Game doesn't load:</strong> Make sure you've uploaded all required files with the correct names</li>
                  <li><strong>Performance issues:</strong> Try reducing the build size in Unity's player settings</li>
                  <li><strong>Upload errors:</strong> Check that your files match the required formats</li>
                  <li><strong>Memory errors:</strong> Consider enabling "Decompression Fallback" in Unity's publishing settings</li>
                </ul>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <div className="flex justify-end">
            <Button onClick={() => setActiveTab('upload')}>
              Continue to Upload
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="upload" className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="grid gap-4">
            <div>
              <Label htmlFor="title">Title (required)</Label>
              <Input
                id="title"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                placeholder="My WebGL Game"
                disabled={isUploading}
              />
            </div>

            <div>
              <Label htmlFor="demoUrl">Demo URL (optional)</Label>
              <Input
                id="demoUrl"
                value={demoUrl}
                onChange={(e) => setDemoUrl(e.target.value)}
                placeholder="https://example.com/my-game"
                disabled={isUploading}
              />
              <p className="text-xs text-muted-foreground mt-1">
                A custom URL where users can play your game (if different from our hosted version)
              </p>
            </div>

            <div>
              <Label htmlFor="zipFile">ZIP File (required)</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="zipFile"
                  type="file"
                  ref={zipFileRef}
                  onChange={(e) => handleFileChange(e, setZipFile)}
                  accept=".zip"
                  disabled={isUploading}
                />
                {zipFile && (
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => clearFile(setZipFile, zipFileRef)}
                    disabled={isUploading}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
              {zipFile && <p className="text-xs text-muted-foreground mt-1">{zipFile.name}</p>}
              <p className="text-xs text-muted-foreground mt-1">
                Upload a ZIP file containing your entire WebGL build folder
              </p>
            </div>

            <div className="flex items-start space-x-2 mt-2">
              <Checkbox
                id="acceptTerms"
                checked={acceptedTerms}
                onCheckedChange={(checked) => setAcceptedTerms(checked as boolean)}
                disabled={isUploading}
              />
              <div className="grid gap-1.5 leading-none">
                <Label
                  htmlFor="acceptTerms"
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  I confirm that I have the rights to upload and share this content
                </Label>
                <p className="text-xs text-muted-foreground">
                  By uploading, you confirm that you own or have permission to share this WebGL build
                </p>
              </div>
            </div>
          </div>
        </TabsContent>


      </Tabs>

      <Button
        onClick={handleUpload}
        disabled={
          isUploading ||
          !title ||
          !acceptedTerms ||
          !zipFile
        }
        className="w-full"
      >
        {isUploading ? (
          <>
            <span className="mr-2">Uploading {uploadProgress}%</span>
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          </>
        ) : (
          <>
            <Upload className="mr-2 h-4 w-4" />
            Upload WebGL Build
          </>
        )}
      </Button>
    </div>
  );
}
