import { NextResponse } from 'next/server';
import { testMinioConnection } from '@/lib/minio';

export async function GET() {
  try {
    // Test the MinIO connection
    const isConnected = await testMinioConnection();
    
    // Return the result
    return NextResponse.json({
      success: true,
      isConnected,
      endpoint: process.env.MINIO_ENDPOINT,
      publicUrl: process.env.MINIO_PUBLIC_URL,
      bucket: process.env.MINIO_BUCKET,
    });
  } catch (error) {
    console.error('Error testing MinIO connection:', error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      endpoint: process.env.MINIO_ENDPOINT,
      publicUrl: process.env.MINIO_PUBLIC_URL,
      bucket: process.env.MINIO_BUCKET,
    }, { status: 500 });
  }
}
