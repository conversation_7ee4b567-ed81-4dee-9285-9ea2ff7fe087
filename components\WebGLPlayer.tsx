'use client';

import React, { useEffect, useRef, useState } from 'react';
import { IMediaFrontend } from '@/models/Media';
import { Button } from '@/components/ui/button';
import { Maximize2, Minimize2 } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';

interface WebGLPlayerProps {
  media: IMediaFrontend;
  className?: string;
  autoLoad?: boolean;
  showFullscreenButton?: boolean;
}

export default function WebGLPlayer({
  media,
  className,
  autoLoad = false,
  showFullscreenButton = true
}: WebGLPlayerProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(!autoLoad);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [gameInstance, setGameInstance] = useState<any>(null);

  // Fixed dimensions for WebGL builds (16:9 aspect ratio)
  const width = 960;
  const height = 540;

  useEffect(() => {
    // Clean up function to handle component unmounting
    return () => {
      if (gameInstance && typeof gameInstance.Quit === 'function') {
        try {
          gameInstance.Quit();
        } catch (e) {
          console.error('Error quitting Unity instance:', e);
        }
      }
    };
  }, [gameInstance]);

  const loadWebGLBuild = () => {
    if (!canvasRef.current) return;

    setIsLoading(true);
    setError(null);
    setProgress(0);

    try {
      // Create a script element to load the Unity loader
      const script = document.createElement('script');
      script.src = media.loaderUrl || '';
      script.async = true;

      script.onload = () => {
        // Access the Unity loader
        if (typeof window.createUnityInstance !== 'function') {
          setError('Unity loader failed to initialize');
          setIsLoading(false);
          return;
        }

        // Configure Unity - updated for modern Unity WebGL builds
        const config = {
          dataUrl: media.dataUrl || '',
          frameworkUrl: media.frameworkUrl || '',
          codeUrl: media.wasmUrl || '', // Modern Unity uses .wasm instead of .code
          streamingAssetsUrl: "StreamingAssets",
          companyName: media.companyName || '',
          productName: media.productName || media.title || 'WebGL Game',
          productVersion: media.buildVersion || '1.0.0',
        };

        // Create Unity instance
        if (!canvasRef.current) {
          setError('Canvas element not found');
          setIsLoading(false);
          return;
        }

        window.createUnityInstance(canvasRef.current, config, (progress: number) => {
          setProgress(Math.round(progress * 100));
        }).then((unityInstance: any) => {
          setGameInstance(unityInstance);
          setIsLoading(false);
        }).catch((error: any) => {
          console.error('Error creating Unity instance:', error);
          setError('Failed to load WebGL build. Please try again.');
          setIsLoading(false);
        });
      };

      script.onerror = () => {
        setError('Failed to load Unity loader script');
        setIsLoading(false);
      };

      document.body.appendChild(script);

      return () => {
        document.body.removeChild(script);
      };
    } catch (error) {
      console.error('Error loading WebGL build:', error);
      setError('An unexpected error occurred while loading the WebGL build');
      setIsLoading(false);
    }
  };

  const toggleFullscreen = async () => {
    if (!containerRef.current) return;

    try {
      if (!isFullscreen) {
        // Enter fullscreen
        if (containerRef.current.requestFullscreen) {
          await containerRef.current.requestFullscreen();
        } else if ((containerRef.current as any).webkitRequestFullscreen) {
          await (containerRef.current as any).webkitRequestFullscreen();
        } else if ((containerRef.current as any).msRequestFullscreen) {
          await (containerRef.current as any).msRequestFullscreen();
        }
        setIsFullscreen(true);
      } else {
        // Exit fullscreen
        if (document.exitFullscreen) {
          await document.exitFullscreen();
        } else if ((document as any).webkitExitFullscreen) {
          await (document as any).webkitExitFullscreen();
        } else if ((document as any).msExitFullscreen) {
          await (document as any).msExitFullscreen();
        }
        setIsFullscreen(false);
      }
    } catch (error) {
      console.error('Error toggling fullscreen:', error);
    }
  };

  // Listen for fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
      document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    };
  }, []);

  // Auto-load if specified
  useEffect(() => {
    if (autoLoad) {
      loadWebGLBuild();
    }
  }, [autoLoad]);

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative overflow-hidden bg-black rounded-lg",
        isFullscreen ? "fixed inset-0 z-50" : "",
        className
      )}
      style={{
        width: isFullscreen ? '100%' : width,
        height: isFullscreen ? '100%' : height
      }}
    >
      {/* Background image if provided */}
      {media.backgroundUrl && !gameInstance && (
        <div
          className="absolute inset-0 bg-cover bg-center opacity-30"
          style={{ backgroundImage: `url(${media.backgroundUrl})` }}
        />
      )}

      {/* Canvas for WebGL content */}
      <canvas
        ref={canvasRef}
        className={cn(
          "w-full h-full",
          isLoading || error ? "hidden" : "block"
        )}
        width={width}
        height={height}
      />

      {/* Loading state */}
      {isLoading && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-70 text-white p-4">
          <h3 className="text-xl font-bold mb-4">{media.title || 'Loading WebGL Game'}</h3>
          <Progress value={progress} className="w-64 mb-2" />
          <p className="text-sm">{progress}% loaded</p>
        </div>
      )}

      {/* Error state */}
      {error && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-70 text-white p-4">
          <h3 className="text-xl font-bold mb-4">Error Loading Game</h3>
          <p className="text-sm text-center mb-4">{error}</p>
          <Button onClick={loadWebGLBuild}>Try Again</Button>
        </div>
      )}

      {/* Initial load button */}
      {!isLoading && !gameInstance && !error && (
        <div className="absolute inset-0 flex flex-col items-center justify-center bg-black bg-opacity-70 text-white p-4">
          <h3 className="text-xl font-bold mb-4">{media.title || 'WebGL Game'}</h3>
          <p className="text-sm text-center mb-4">Click to load and play</p>
          <Button onClick={loadWebGLBuild} size="lg">
            Play Game
          </Button>
        </div>
      )}

      {/* Fullscreen button */}
      {showFullscreenButton && gameInstance && (
        <Button
          variant="secondary"
          size="icon"
          className="absolute top-2 right-2 bg-black bg-opacity-50 hover:bg-opacity-70 z-10"
          onClick={toggleFullscreen}
        >
          {isFullscreen ? (
            <Minimize2 className="h-4 w-4" />
          ) : (
            <Maximize2 className="h-4 w-4" />
          )}
        </Button>
      )}
    </div>
  );
}

// Add the createUnityInstance to the Window interface
declare global {
  interface Window {
    createUnityInstance: (
      canvas: HTMLCanvasElement,
      config: any,
      progressCallback: (progress: number) => void
    ) => Promise<any>;
  }
}
