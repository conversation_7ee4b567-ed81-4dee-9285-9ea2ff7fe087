'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface AnalyticsData {
	totalUsers: number;
	totalProjects: number;
	totalProducts: number;
}

export default function AnalyticsDashboard() {
	const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(
		null
	);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchAnalytics = async () => {
			try {
				const response = await fetch('/api/dashboard/analytics');
				if (!response.ok) {
					throw new Error('Failed to fetch analytics data');
				}
				const data: AnalyticsData = await response.json();
				setAnalyticsData(data);
				console.log(data);
			} catch (err) {
				console.error('Error fetching analytics data:', err);
				setError('Failed to load analytics data. Please try again later.');
			}
		};

		fetchAnalytics();
	}, []);

	if (error) {
		return (
			<Alert variant="destructive">
				<AlertDescription>{error}</AlertDescription>
			</Alert>
		);
	}

	if (!analyticsData) {
		return <div>Loading analytics...</div>;
	}

	return (
		<div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
			<Card>
				<CardHeader>
					<CardTitle>Total Users</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-2xl font-bold">{analyticsData.totalUsers}</div>
				</CardContent>
			</Card>
			<Card>
				<CardHeader>
					<CardTitle>Total Projects</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-2xl font-bold">
						{analyticsData.totalProjects}
					</div>
				</CardContent>
			</Card>
			<Card>
				<CardHeader>
					<CardTitle>Total Products</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-2xl font-bold">
						{analyticsData.totalProducts}
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
