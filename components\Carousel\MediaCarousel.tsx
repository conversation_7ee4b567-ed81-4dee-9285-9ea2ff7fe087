'use client';

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight, Gamepad2, X } from 'lucide-react';
import Image from 'next/image';
import MinioImage from '@/components/MinioImage';
import WebGLPlayer from '@/components/WebGLPlayer';

interface MediaItem {
	type: 'image' | 'youtube' | 'webgl';
	url: string;
	width?: number; // For WebGL canvas width
	height?: number; // For WebGL canvas height
	title?: string; // For WebGL build title
	loaderUrl?: string; // For WebGL loader file
	dataUrl?: string; // For WebGL data file
	frameworkUrl?: string; // For WebGL framework file
	codeUrl?: string; // For WebGL code file
	memoryUrl?: string; // For WebGL memory file (optional)
	backgroundUrl?: string; // For WebGL background image (optional)
	companyName?: string; // For WebGL company name
	productName?: string; // For WebGL product name
	buildVersion?: string; // For WebGL build version
}

interface MediaCarouselProps {
	items: MediaItem[];
}

export function MediaCarousel({ items }: MediaCarouselProps) {
	const [currentSlide, setCurrentSlide] = useState(0);
	const [isModalOpen, setIsModalOpen] = useState(false);
	const [isPlaying, setIsPlaying] = useState(false);
	const [isMounted, setIsMounted] = useState(false);
	const [isModalLoading, setIsModalLoading] = useState(false);
	const videoRef = useRef<HTMLIFrameElement>(null);
	const timerRef = useRef<NodeJS.Timeout | null>(null);

	// Handle hydration issues by only rendering on client
	useEffect(() => {
		setIsMounted(true);
	}, []);

	const nextSlide = useCallback(() => {
		if (!isPlaying && !isModalOpen) {
			setCurrentSlide((prev) => (prev + 1) % items.length);
		}
	}, [items.length, isPlaying, isModalOpen]);

	const prevSlide = useCallback(() => {
		if (!isPlaying && !isModalOpen) {
			setCurrentSlide((prev) => (prev - 1 + items.length) % items.length);
		}
	}, [items.length, isPlaying, isModalOpen]);

	const resetTimer = useCallback(() => {
		if (timerRef.current) {
			clearTimeout(timerRef.current);
		}
		if (!isPlaying && !isModalOpen) {
			timerRef.current = setTimeout(nextSlide, 5000);
		}
	}, [nextSlide, isPlaying, isModalOpen]);

	useEffect(() => {
		resetTimer();
		return () => {
			if (timerRef.current) {
				clearTimeout(timerRef.current);
			}
		};
	}, [currentSlide, resetTimer]);

	const handleVideoStateChange = useCallback(
		(event: MessageEvent) => {
			if (event.data && typeof event.data === 'string') {
				const data = JSON.parse(event.data);
				if (data.event === 'onStateChange') {
					if (data.info === 1) {
						setIsPlaying(true);
					} else if (data.info === 0) {
						setIsPlaying(false);
						if (!isModalOpen) {
							nextSlide();
						}
					} else if (data.info === 2) {
						setIsPlaying(false);
						resetTimer();
					}
				}
			}
		},
		[nextSlide, resetTimer, isModalOpen]
	);

	useEffect(() => {
		window.addEventListener('message', handleVideoStateChange);
		return () => {
			window.removeEventListener('message', handleVideoStateChange);
		};
	}, [handleVideoStateChange]);

	const openModal = () => {
		setIsModalLoading(true);
		setIsModalOpen(true);
		if (timerRef.current) {
			clearTimeout(timerRef.current);
		}
		// Reset loading state after a short delay to ensure image has time to load
		setTimeout(() => {
			setIsModalLoading(false);
		}, 500);
	};

	const closeModal = () => {
		setIsModalOpen(false);
		setIsModalLoading(false);
		resetTimer();
	};

	const handleSlideChange = (newIndex: number) => {
		if (isPlaying && videoRef.current) {
			videoRef.current.contentWindow?.postMessage(
				'{"event":"command","func":"pauseVideo","args":""}',
				'*'
			);
		}

		// If we're in modal view and changing to an image, show loading state
		if (isModalOpen && items[newIndex].type === 'image') {
			setIsModalLoading(true);
			// Reset loading state after a short delay to ensure image has time to load
			setTimeout(() => {
				setIsModalLoading(false);
			}, 500);
		}

		setCurrentSlide(newIndex);
		resetTimer();
	};

	// If not mounted yet (server-side), render a placeholder
	if (!isMounted) {
		return (
			<div className="relative w-full aspect-video overflow-hidden bg-gray-900 flex items-center justify-center">
				<div className="animate-pulse w-full h-full bg-gray-800"></div>
			</div>
		);
	}

	return (
		<>
			<div className="relative w-full aspect-video overflow-hidden bg-gray-900">
				{items.map((item, index) => (
					<div
						key={index}
						className={`absolute top-0 left-0 w-full h-full transition-opacity duration-1000 ${
							index === currentSlide ? 'opacity-100' : 'opacity-0'
						}`}
						onClick={item.type === 'image' ? openModal : undefined}
						style={{ cursor: item.type === 'image' ? 'pointer' : 'default' }}
					>
						{item.type === 'image' ? (
							<MinioImage
								src={item.url}
								alt="Project media"
								fill
								style={{ objectFit: 'cover' }}
								className="w-full h-full"
							/>
						) : item.type === 'webgl' ? (
							<WebGLPlayer
								media={{
									...item,
									type: 'webgl',
									status: 'success'
								}}
								autoLoad={false}
								showFullscreenButton={false}
							/>
						) : (
							<iframe
								ref={videoRef}
								src={`${
									item.url
								}?enablejsapi=1&autoplay=1&mute=1&controls=0&loop=1&playlist=${
									item.url.split('/').pop() || ''
								}`}
								title="YouTube video player"
								frameBorder="0"
								allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
								allowFullScreen
								className="w-full h-full"
							/>
						)}
					</div>
				))}

				<div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
					{items.map((_, index) => (
						<button
							key={index}
							className={`w-2 h-2 rounded-full ${
								index === currentSlide ? 'bg-blue-400' : 'bg-gray-400'
							}`}
							onClick={() => handleSlideChange(index)}
							aria-label={`Go to slide ${index + 1}`}
						/>
					))}
				</div>

				<Button
					variant="ghost"
					size="icon"
					className="absolute left-2 md:left-4 top-1/2 transform -translate-y-1/2 text-white bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full w-10 h-10 z-10 touch-manipulation"
					onClick={() =>
						handleSlideChange((currentSlide - 1 + items.length) % items.length)
					}
					aria-label="Previous slide"
				>
					<ChevronLeft className="w-6 h-6" />
				</Button>
				<Button
					variant="ghost"
					size="icon"
					className="absolute right-2 md:right-4 top-1/2 transform -translate-y-1/2 text-white bg-black bg-opacity-50 hover:bg-opacity-70 rounded-full w-10 h-10 z-10 touch-manipulation"
					onClick={() => handleSlideChange((currentSlide + 1) % items.length)}
					aria-label="Next slide"
				>
					<ChevronRight className="w-6 h-6" />
				</Button>
			</div>

			{isModalOpen && (
				<div
					className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-40 overflow-hidden"
					onClick={closeModal} // Close modal when clicking the backdrop
					onTouchEnd={(e) => {
						// Only close if the touch ends on the backdrop itself
						if (e.target === e.currentTarget) {
							closeModal();
						}
					}}
				>
					<div
						className="relative w-full h-full max-w-4xl max-h-4xl flex items-center justify-center touch-manipulation"
						onClick={(e) => e.stopPropagation()} // Prevent clicks on content from closing modal
						onTouchEnd={(e) => e.stopPropagation()} // Prevent touch events on content from closing modal
					>
						<Button
							variant="ghost"
							size="icon"
							className="fixed top-4 right-4 text-white z-50 bg-black bg-opacity-70 hover:bg-opacity-90 rounded-full w-12 h-12 touch-manipulation shadow-lg"
							onClick={(e) => {
								e.stopPropagation();
								closeModal();
							}}
							aria-label="Close modal"
						>
							<X className="w-6 h-6" />
						</Button>
						{items[currentSlide].type === 'image' ? (
							<div className="relative w-full h-full flex items-center justify-center">
								{isModalLoading && (
									<div className="absolute inset-0 flex items-center justify-center z-20">
										<div className="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
									</div>
								)}
								<div
									className="relative w-full h-full max-w-4xl max-h-4xl touch-manipulation"
									onTouchStart={(e) => {
										// Record the starting touch position
										const touchX = e.touches[0].clientX;
										(e.currentTarget as any).touchStartX = touchX;
									}}
									onTouchEnd={(e) => {
										// Get the ending touch position
										const touchEndX = e.changedTouches[0].clientX;
										const touchStartX = (e.currentTarget as any).touchStartX;

										// Calculate the swipe distance
										const swipeDistance = touchEndX - touchStartX;

										// If the swipe is significant enough (more than 50px)
										if (Math.abs(swipeDistance) > 50) {
											if (swipeDistance > 0) {
												// Swipe right - go to previous slide
												handleSlideChange((currentSlide - 1 + items.length) % items.length);
											} else {
												// Swipe left - go to next slide
												handleSlideChange((currentSlide + 1) % items.length);
											}
										}
										e.stopPropagation();
									}}
								>
									<MinioImage
										src={items[currentSlide].url}
										alt="Enlarged project media"
										fill
										style={{ objectFit: 'contain' }}
										priority={true}
										className="z-10 touch-manipulation"
										onLoadingComplete={() => setIsModalLoading(false)}
									/>
								</div>
							</div>
						) : items[currentSlide].type === 'webgl' ? (
							<div className="relative w-full h-full flex items-center justify-center">
								<WebGLPlayer
									media={{
										...items[currentSlide],
										type: 'webgl',
										status: 'success'
									}}
									autoLoad={true}
									showFullscreenButton={true}
									className="w-full h-full max-w-4xl max-h-4xl"
								/>
							</div>
						) : (
							<iframe
								src={`${items[currentSlide].url}?autoplay=1&controls=1`}
								title="YouTube video player"
								frameBorder="0"
								allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
								allowFullScreen
								className="w-full h-full"
							/>
						)}
						<Button
							variant="ghost"
							size="icon"
							className="fixed left-2 md:left-4 top-1/2 transform -translate-y-1/2 text-white bg-black bg-opacity-70 hover:bg-opacity-90 rounded-full w-12 h-12 touch-manipulation z-50 shadow-lg"
							onClick={(e) => {
								e.stopPropagation(); // Prevent closing the modal
								handleSlideChange((currentSlide - 1 + items.length) % items.length);
							}}
							aria-label="Previous slide"
						>
							<ChevronLeft className="w-6 h-6" />
						</Button>
						<Button
							variant="ghost"
							size="icon"
							className="fixed right-2 md:right-4 top-1/2 transform -translate-y-1/2 text-white bg-black bg-opacity-70 hover:bg-opacity-90 rounded-full w-12 h-12 touch-manipulation z-50 shadow-lg"
							onClick={(e) => {
								e.stopPropagation(); // Prevent closing the modal
								handleSlideChange((currentSlide + 1) % items.length);
							}}
							aria-label="Next slide"
						>
							<ChevronRight className="w-6 h-6" />
						</Button>
					</div>
					<div className="fixed bottom-8 left-0 right-0 flex justify-center space-x-2 z-50">
						<div
							className="bg-black bg-opacity-70 px-4 py-3 rounded-full flex space-x-2 touch-manipulation shadow-lg"
							onClick={(e) => e.stopPropagation()} // Prevent clicks from closing modal
							onTouchEnd={(e) => e.stopPropagation()} // Prevent touch events from closing modal
						>
							{items.map((_, index) => (
								<button
									key={index}
									className={`w-4 h-4 rounded-full ${index === currentSlide ? 'bg-blue-400' : 'bg-gray-400'} touch-manipulation mx-1`}
									onClick={(e) => {
										e.stopPropagation();
										handleSlideChange(index);
									}}
									aria-label={`Go to slide ${index + 1}`}
								/>
							))}
						</div>
					</div>
				</div>
			)}
		</>
	);
}

export default MediaCarousel;
