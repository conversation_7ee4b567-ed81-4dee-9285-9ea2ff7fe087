import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongoose';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import User from '@/models/User';
import Project from '@/models/Project';
import Product from '@/models/Product';
import ApiToken from '@/models/ApiToken';
import { cookies, headers } from 'next/headers';

export const dynamic = 'force-dynamic';

export async function GET(request: Request) {
  try {
    // Explicitly await cookies and headers to avoid warnings
    const cookiesList = await cookies();
    const headersList = await headers();

    // Await authOptions to properly handle cookies and headers
    const options = await authOptions();
    const session = await getServerSession(options);
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    await connectToDatabase();

    // Count documents in parallel for better performance
    const [projects, products, users, apiTokens, totalApiUsage] = await Promise.all([
      Project.countDocuments(),
      Product.countDocuments(),
      User.countDocuments(),
      ApiToken.countDocuments(),
      ApiToken.aggregate([
        {
          $group: {
            _id: null,
            totalUsage: { $sum: { $ifNull: ['$usageCount', 0] } }
          }
        }
      ])
    ]);

    // Extract total API usage from aggregation result
    const totalUsage = totalApiUsage.length > 0 ? totalApiUsage[0].totalUsage : 0;

    return NextResponse.json({
      projects,
      products,
      users,
      apiTokens,
      apiUsage: totalUsage
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      {
        message: 'An error occurred while fetching dashboard stats',
        error: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
