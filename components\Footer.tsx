import Link from 'next/link';
import { siteConfig } from '@/lib/siteConfig';
import {
	FaDiscord,
	FaFacebook,
	FaLinkedin,
	FaYoutube,
	FaUnity,
	FaEnvelope
} from 'react-icons/fa';

export default function Footer() {
	const currentYear = new Date().getFullYear();

	return (
		<footer className="bg-background text-foreground py-4 border-t border-border">
			<div className="container mx-auto px-4">
				<div className="flex flex-col md:flex-row justify-between items-center">
					<div className="mb-4 md:mb-0 text-sm flex flex-wrap items-center">
						<p className="mr-4">&copy; {currentYear} RealSoft Games. All rights reserved.</p>
						<div className="flex space-x-4">
							<Link href={siteConfig.legal.privacyPolicy} className="hover:text-muted-foreground transition-colors">
								Privacy Policy
							</Link>
							<Link href={siteConfig.legal.termsOfService} className="hover:text-muted-foreground transition-colors">
								Terms of Service
							</Link>
						</div>
					</div>
					<div className="flex space-x-4">
						<Link
							href={siteConfig.social.discord}
							target="_blank"
							rel="noopener noreferrer"
							aria-label="Discord"
						>
							<span className="inline-block p-2 hover:bg-muted rounded-full transition-colors">
								<FaDiscord className="text-xl" />
							</span>
						</Link>
						<Link
							href={siteConfig.social.facebook}
							target="_blank"
							rel="noopener noreferrer"
							aria-label="Facebook"
						>
							<span className="inline-block p-2 hover:bg-muted rounded-full transition-colors">
								<FaFacebook className="text-xl" />
							</span>
						</Link>
						<Link
							href={siteConfig.social.linkedin}
							target="_blank"
							rel="noopener noreferrer"
							aria-label="LinkedIn"
						>
							<span className="inline-block p-2 hover:bg-muted rounded-full transition-colors">
								<FaLinkedin className="text-xl" />
							</span>
						</Link>
						<Link
							href={siteConfig.social.youtube}
							target="_blank"
							rel="noopener noreferrer"
							aria-label="YouTube"
						>
							<span className="inline-block p-2 hover:bg-muted rounded-full transition-colors">
								<FaYoutube className="text-xl" />
							</span>
						</Link>
						<Link
							href={siteConfig.social.unityAssetStore}
							target="_blank"
							rel="noopener noreferrer"
							aria-label="Unity Asset Store"
						>
							<span className="inline-block p-2 hover:bg-muted rounded-full transition-colors">
								<FaUnity className="text-xl" />
							</span>
						</Link>
						<Link href="/contact" aria-label="Contact">
							<span className="inline-block p-2 hover:bg-muted rounded-full transition-colors">
								<FaEnvelope className="text-xl" />
							</span>
						</Link>
					</div>
				</div>
			</div>
		</footer>
	);
}
