import React from 'react';
import { But<PERSON> } from '@nextui-org/react';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from '@/components/ui/select';
import { UserRole } from '@/types/global';
import {
	<PERSON>,
	CardHeader,
	CardContent,
	CardFooter
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { User } from '@/models/User'; // Updated import

interface EditUserModalProps {
	isOpen: boolean;
	onClose: () => void;
	user: User | null;
	onUpdate: (updatedUser: Partial<User>) => void;
	countries: { value: string; label: string }[];
}

export function EditUserModal({
	isOpen,
	onClose,
	user,
	onUpdate,
	countries
}: EditUserModalProps) {
	const [editedUser, setEditedUser] = React.useState<Partial<User> | null>(
		null
	);

	React.useEffect(() => {
		if (user) {
			setEditedUser({ ...user });
		}
	}, [user]);

	const handleInputChange = (field: keyof User, value: string) => {
		setEditedUser((prev) => (prev ? { ...prev, [field]: value } : null));
	};

	const handleSave = () => {
		if (editedUser) {
			onUpdate(editedUser);
		}
	};

	const handleCancel = () => {
		setEditedUser(null);
		onClose();
	};

	if (!isOpen || !editedUser) return null;

	return (
		<Card className="w-full max-w-md mx-auto">
			<CardHeader>
				<h2 className="text-lg font-semibold">Edit User</h2>
			</CardHeader>
			<CardContent>
				<div className="space-y-4">
					<div className="space-y-2">
						<Label htmlFor="username">Username</Label>
						<Input
							id="username"
							value={editedUser.username}
							onChange={(e) => handleInputChange('username', e.target.value)}
						/>
					</div>
					<div className="space-y-2">
						<Label htmlFor="name">Name</Label>
						<Input
							id="name"
							value={editedUser.name}
							onChange={(e) => handleInputChange('name', e.target.value)}
						/>
					</div>
					<div className="space-y-2">
						<Label htmlFor="email">Email</Label>
						<Input
							id="email"
							value={editedUser.email}
							onChange={(e) => handleInputChange('email', e.target.value)}
						/>
					</div>
					<div className="space-y-2">
						<Label htmlFor="dateOfBirth">Date of Birth</Label>
						<Input
							id="dateOfBirth"
							type="date"
							value={editedUser.dateOfBirth}
							onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
						/>
					</div>
					<div className="space-y-2">
						<Label htmlFor="country">Country</Label>
						<Select
							value={editedUser.country}
							onValueChange={(value) => handleInputChange('country', value)}
						>
							<SelectTrigger id="country">
								<SelectValue placeholder="Select a country" />
							</SelectTrigger>
							<SelectContent>
								{countries.map((country) => (
									<SelectItem key={country.value} value={country.value}>
										{country.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
					</div>
					<div className="space-y-2">
						<Label htmlFor="role">Role</Label>
						<Select
							value={editedUser.role}
							onValueChange={(value) =>
								handleInputChange('role', value as UserRole)
							}
						>
							<SelectTrigger id="role">
								<SelectValue placeholder="Select a role" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value={UserRole.USER}>User</SelectItem>
								<SelectItem value={UserRole.ADMIN}>Admin</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</div>
			</CardContent>
			<CardFooter className="flex justify-end space-x-2">
				<Button color="danger" variant="light" onClick={handleCancel}>
					Cancel
				</Button>
				<Button color="primary" onClick={handleSave}>
					Save Changes
				</Button>
			</CardFooter>
		</Card>
	);
}
