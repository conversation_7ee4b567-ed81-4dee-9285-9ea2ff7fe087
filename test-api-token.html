<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Token Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ccc;
            padding: 20px;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>API Token Test</h1>
    <div class="container">
        <div class="form-group">
            <label for="apiToken">API Token:</label>
            <input type="text" id="apiToken" placeholder="Enter your API token">
        </div>
        <div class="form-group">
            <label for="endpoint">API Endpoint:</label>
            <select id="endpoint">
                <option value="/api/test-token">Test Token</option>
                <option value="/api/public/api-test">Public API Test</option>
                <option value="/api/public/products">Public Products</option>
            </select>
        </div>
        <button id="testButton">Test API Token</button>
        <div class="result" id="result">Results will appear here...</div>
    </div>

    <script>
        document.getElementById('testButton').addEventListener('click', async () => {
            const apiToken = document.getElementById('apiToken').value;
            const endpoint = document.getElementById('endpoint').value;
            const resultElement = document.getElementById('result');
            
            if (!apiToken) {
                resultElement.textContent = 'Please enter an API token';
                return;
            }
            
            resultElement.textContent = 'Testing API token...';
            
            try {
                const response = await fetch(`http://localhost:3000${endpoint}`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiToken}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultElement.textContent = 'API token test successful!\n\n' + JSON.stringify(data, null, 2);
                } else {
                    resultElement.textContent = `API request failed with status ${response.status}\n\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultElement.textContent = `Error testing API token: ${error.message}`;
            }
        });
    </script>
</body>
</html>
