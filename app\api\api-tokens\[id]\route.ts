import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongoose';
import ApiToken from '@/models/ApiToken';
import { UserRole } from '@/types/global';
import crypto from 'crypto';

export const dynamic = 'force-dynamic';

// Regenerate an API token
export async function PUT(
  _request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Explicitly await cookies to avoid the sync dynamic APIs warning
    const options = await authOptions();
    const session = await getServerSession(options);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can regenerate API tokens
    if (session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    await connectToDatabase();

    const { id } = await params;

    // Find the token and ensure it belongs to the current user
    const apiToken = await ApiToken.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!apiToken) {
      return NextResponse.json({ error: 'Token not found' }, { status: 404 });
    }

    // Generate a new token value
    const newToken = crypto.randomBytes(32).toString('hex');
    apiToken.token = newToken;
    await apiToken.save();

    // Return the regenerated token
    return NextResponse.json({
      _id: apiToken._id.toString(),
      name: apiToken.name,
      token: apiToken.token,
      createdAt: apiToken.createdAt,
      updatedAt: apiToken.updatedAt
    });
  } catch (error) {
    console.error('Error regenerating API token:', error);
    return NextResponse.json(
      { error: 'Failed to regenerate API token' },
      { status: 500 }
    );
  }
}

// Delete an API token
export async function DELETE(
  _request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Explicitly await cookies to avoid the sync dynamic APIs warning
    const options = await authOptions();
    const session = await getServerSession(options);
    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Only admins can delete API tokens
    if (session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    await connectToDatabase();

    const { id } = await params;

    // Find the token and ensure it belongs to the current user
    const apiToken = await ApiToken.findOne({
      _id: id,
      userId: session.user.id
    });

    if (!apiToken) {
      return NextResponse.json({ error: 'Token not found' }, { status: 404 });
    }

    // Delete the token
    await ApiToken.deleteOne({ _id: id });

    return NextResponse.json({ message: 'Token deleted successfully' });
  } catch (error) {
    console.error('Error deleting API token:', error);
    return NextResponse.json(
      { error: 'Failed to delete API token' },
      { status: 500 }
    );
  }
}
