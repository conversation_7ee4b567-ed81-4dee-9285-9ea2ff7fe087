'use client';

import { SessionProvider } from 'next-auth/react';
import { ThemeProvider } from 'next-themes';
import React from 'react';
import { AvatarProvider } from '@/contexts/AvatarContext';
import { ToastProvider } from '@/hooks/use-toast';

export function Providers({ children }: { children: React.ReactNode }) {
	return (
		<SessionProvider>
			<ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
				<ToastProvider>
					<AvatarProvider>
						{children}
					</AvatarProvider>
				</ToastProvider>
			</ThemeProvider>
		</SessionProvider>
	);
}
