'use client';

import React, { useState, useEffect, useC<PERSON>back, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { IProject } from '@/models/Project';
import { Status, StatusFilter } from '@/types/global';
import ProjectForm from '@/components/Project/ProjectForm';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import update from 'immutability-helper';
import { useToast } from '@/hooks/use-toast';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	<PERSON>ertDialog<PERSON>ead<PERSON>,
	<PERSON>ertDialogTitle,
	AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from '@/components/ui/select';
import { ChevronUp, ChevronDown } from 'lucide-react';
import ClientImage from '@/components/ClientImage';

interface ProjectListProps {
	projects: IProject[];
	onEdit: (id: string) => void;
	onDelete: (id: string) => void;
	onChangeStatus: (id: string, newStatus: Status) => void;
	onToggleFeatured: (id: string) => void;
	onReorder: (newProjects: IProject[]) => void;
}

interface DraggableProjectCardProps {
	project: IProject;
	index: number;
	moveProject: (dragIndex: number, hoverIndex: number) => void;
	onEdit: (id: string) => void;
	onDelete: (id: string) => void;
	onChangeStatus: (id: string, newStatus: Status) => void;
	onToggleFeatured: (id: string) => void;
}

function DraggableProjectCard({
	project,
	index,
	moveProject,
	onEdit,
	onDelete,
	onChangeStatus,
	onToggleFeatured,
	isMobile,
	totalItems
}: DraggableProjectCardProps & { isMobile: boolean; totalItems: number }) {
	const ref = useRef<HTMLDivElement>(null);
	const [, drop] = useDrop<
		{ index: number },
		void,
		{ handlerId: string | symbol | null }
	>({
		accept: 'project',
		hover(item: { index: number }, monitor) {
			if (!ref.current) {
				return;
			}
			const dragIndex = item.index;
			const hoverIndex = index;
			if (dragIndex === hoverIndex) {
				return;
			}
			moveProject(dragIndex, hoverIndex);
			item.index = hoverIndex;
		}
	});

	const [{ isDragging }, drag] = useDrag({
		type: 'project',
		item: () => ({ id: project._id, index }),
		collect: (monitor) => ({
			isDragging: monitor.isDragging()
		})
	});

	drag(drop(ref));

	return (
		<div ref={ref} style={{ opacity: isDragging ? 0.5 : 1 }}>
			<Card key={project._id.toString()} className="flex flex-col">
				<CardHeader>
					<div className="flex justify-between items-start">
						<div className="flex items-center gap-2">
							<div className={`w-3 h-3 rounded-full ${project.status === Status.Published ? 'bg-green-500' : project.status === Status.Draft ? 'bg-yellow-500' : 'bg-red-500'}`}
								title={project.status}
							/>
							<CardTitle className="text-lg">{project.title}</CardTitle>
						</div>
						<div className="flex flex-col items-end space-y-1">
							<Badge
								variant={
									project.status === Status.Published ? 'default' : 'secondary'
								}
							>
								{project.status}
							</Badge>
							{project.isFeatured && <Badge variant="warning">Featured</Badge>}
						</div>
					</div>
				</CardHeader>
				<CardContent className="flex-grow">
					{project.media && project.media.length > 0 && (
						<div className="relative w-full h-40 mb-4">
							{project.media[0].type === 'image' ? (
								<ClientImage
									src={project.media[0].url}
									alt={project.title}
									className="rounded-md w-full h-full object-cover"
									style={{ objectFit: 'cover' }}
								/>
							) : (
								<iframe
									src={project.media[0].url}
									title={project.title}
									className="w-full h-full rounded-md"
									allowFullScreen
								/>
							)}
						</div>
					)}
					<Badge variant="success" className="mt-2">
						{Number(project.price) === 0 ? 'Free' : `$${project.price}`}
					</Badge>
				</CardContent>
				<div className="p-4 bg-muted/50 flex justify-between items-center">
					<div className="flex space-x-2">
						<Button
							variant="outline"
							size="sm"
							onClick={() => onEdit(project._id.toString())}
						>
							Edit
						</Button>
						{project.status === Status.Archived ? (
							<>
								<Button
									variant="outline"
									size="sm"
									onClick={() =>
										onChangeStatus(project._id.toString(), Status.Draft)
									}
								>
									Restore
								</Button>
								<AlertDialog>
									<AlertDialogTrigger asChild>
										<Button variant="destructive" size="sm">
											Delete Permanently
										</Button>
									</AlertDialogTrigger>
									<AlertDialogContent>
										<AlertDialogHeader>
											<AlertDialogTitle>
												Are you absolutely sure?
											</AlertDialogTitle>
											<AlertDialogDescription>
												This action cannot be undone. This will permanently
												delete the project and remove it from our servers.
											</AlertDialogDescription>
										</AlertDialogHeader>
										<AlertDialogFooter>
											<AlertDialogCancel>Cancel</AlertDialogCancel>
											<AlertDialogAction
												onClick={() => onDelete(project._id.toString())}
											>
												Yes, delete permanently
											</AlertDialogAction>
										</AlertDialogFooter>
									</AlertDialogContent>
								</AlertDialog>
							</>
						) : (
							<Button
								variant="outline"
								size="sm"
								onClick={() =>
									onChangeStatus(project._id.toString(), Status.Archived)
								}
							>
								Archive
							</Button>
						)}
					</div>
					{isMobile && (
						<div className="flex flex-col space-y-1">
							<Button
								variant="outline"
								size="sm"
								onClick={() => moveProject(index, index - 1)}
								disabled={index === 0}
							>
								<ChevronUp className="h-4 w-4" />
							</Button>
							<Button
								variant="outline"
								size="sm"
								onClick={() => moveProject(index, index + 1)}
								disabled={index === totalItems - 1}
							>
								<ChevronDown className="h-4 w-4" />
							</Button>
						</div>
					)}
				</div>
			</Card>
		</div>
	);
}

function ProjectList({
	projects,
	onEdit,
	onDelete,
	onChangeStatus,
	onToggleFeatured,
	onReorder,
	isMobile
}: ProjectListProps & { isMobile: boolean }) {
	const moveProject = useCallback(
		(dragIndex: number, hoverIndex: number) => {
			if (
				dragIndex < 0 ||
				hoverIndex < 0 ||
				dragIndex >= projects.length ||
				hoverIndex >= projects.length
			) {
				return;
			}
			onReorder(
				update(projects, {
					$splice: [
						[dragIndex, 1],
						[hoverIndex, 0, projects[dragIndex]]
					]
				})
			);
		},
		[projects, onReorder]
	);

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
			{projects.map((project, index) => (
				<DraggableProjectCard
					key={project._id.toString()}
					project={project}
					index={index}
					moveProject={moveProject}
					onEdit={onEdit}
					onDelete={onDelete}
					onChangeStatus={onChangeStatus}
					onToggleFeatured={onToggleFeatured}
					isMobile={isMobile}
					totalItems={projects.length}
				/>
			))}
		</div>
	);
}

export default function ProjectsPage() {
	const [projects, setProjects] = useState<IProject[]>([]);
	const [activeTab, setActiveTab] = useState<StatusFilter>(StatusFilter.All);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [showForm, setShowForm] = useState(false);
	const [editingProject, setEditingProject] = useState<IProject | null>(null);
	const { toast } = useToast();
	const [originalOrder, setOriginalOrder] = useState<IProject[]>([]);
	const [hasOrderChanged, setHasOrderChanged] = useState(false);
	const [isMobile, setIsMobile] = useState(false);

	useEffect(() => {
		fetchProjects();
		const checkIfMobile = () => setIsMobile(window.innerWidth < 768);
		checkIfMobile();
		window.addEventListener('resize', checkIfMobile);

		// Check for 'new' query parameter
		const urlParams = new URLSearchParams(window.location.search);
		if (urlParams.get('new') === 'true') {
			setShowForm(true);
			// Update URL without refreshing the page
			const newUrl = window.location.pathname;
			window.history.pushState({}, '', newUrl);
		}

		return () => window.removeEventListener('resize', checkIfMobile);
	}, []);

	const fetchProjects = async () => {
		setIsLoading(true);
		setError(null);
		try {
			const response = await fetch('/api/projects', {
				credentials: 'include'
			});
			if (!response.ok) {
				const errorData = await response.json().catch(() => ({}));
				throw new Error(errorData.error || 'Failed to fetch projects');
			}
			const data = await response.json();
			const sortedProjects = data.sort(
				(a: IProject, b: IProject) => a.order - b.order
			);
			setProjects(sortedProjects);
			setOriginalOrder(sortedProjects);
		} catch (error) {
			console.error('Error fetching projects:', error);
			setError('Failed to load projects. Please try again.');
		} finally {
			setIsLoading(false);
		}
	};

	const handleDelete = async (id: string) => {
		try {
			const response = await fetch(`/api/projects/${id}`, {
				method: 'DELETE',
				credentials: 'include'
			});
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.error || 'Failed to delete project');
			}
			setProjects(projects.filter((project) => project._id.toString() !== id));
			toast({
				title: 'Success',
				description: 'Project and associated media deleted successfully',
				type: 'success'
			});
		} catch (error) {
			console.error('Error deleting project:', error);
			toast({
				title: 'Error',
				description:
					error instanceof Error
						? error.message
						: 'Failed to delete project and associated media. Please try again.',
				type: 'error'
			});
		}
	};

	const handleEdit = (id: string) => {
		const projectToEdit = projects.find((p) => p._id.toString() === id);
		if (projectToEdit) {
			setEditingProject(projectToEdit);
			setShowForm(true);
		}
	};

	const handleChangeStatus = async (id: string, newStatus: Status) => {
		try {
			const response = await fetch(`/api/projects/${id}`, {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ status: newStatus }),
				credentials: 'include'
			});
			if (!response.ok) {
				throw new Error('Failed to update project status');
			}
			const updatedProject = await response.json();
			setProjects(
				projects.map((project) =>
					project._id.toString() === id ? updatedProject : project
				)
			);
			toast({
				title: 'Success',
				description: `Project ${
					newStatus === Status.Archived ? 'archived' : 'status updated'
				} successfully`,
				type: 'success'
			});
		} catch (error) {
			console.error('Error updating project status:', error);
			toast({
				title: 'Error',
				description: 'Failed to update project status. Please try again.',
				type: 'error'
			});
		}
	};

	const handleToggleFeatured = async (id: string) => {
		try {
			const project = projects.find((p) => p._id.toString() === id);
			if (!project) return;

			const response = await fetch(`/api/projects/${id}`, {
				method: 'PATCH',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ isFeatured: !project.isFeatured }),
				credentials: 'include'
			});
			if (!response.ok) {
				throw new Error('Failed to toggle featured status');
			}
			setProjects(
				projects.map((p) =>
					p._id.toString() === id
						? ({ ...p, isFeatured: !p.isFeatured } as IProject)
						: p
				)
			);
			toast({
				title: 'Success',
				description: `Project ${
					project.isFeatured ? 'unfeatured' : 'featured'
				} successfully`,
				type: 'success'
			});
		} catch (error) {
			console.error('Error toggling featured status:', error);
			toast({
				title: 'Error',
				description: 'Failed to update featured status. Please try again.',
				type: 'error'
			});
		}
	};

	const handleCreateOrUpdateProject = async (
		projectData: Partial<IProject>
	) => {
		try {
			const url = editingProject
				? `/api/projects/${editingProject._id}`
				: '/api/projects';
			const method = editingProject ? 'PATCH' : 'POST';

			const response = await fetch(url, {
				method,
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(projectData),
				credentials: 'include'
			});

			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(
					errorData.error ||
						`Failed to ${editingProject ? 'update' : 'create'} project`
				);
			}

			const updatedProject = await response.json();

			if (editingProject) {
				setProjects(
					projects.map((p) =>
						p._id === updatedProject._id ? updatedProject : p
					)
				);
				toast({
					title: 'Success',
					description: 'Project updated successfully',
					type: 'success'
				});
			} else {
				setProjects([...projects, updatedProject]);
				toast({
					title: 'Success',
					description: 'Project created successfully',
					type: 'success'
				});
			}

			setShowForm(false);
			setEditingProject(null);
		} catch (error) {
			console.error(
				`Error ${editingProject ? 'updating' : 'creating'} project:`,
				error
			);
			toast({
				title: 'Error',
				description:
					error instanceof Error
						? error.message
						: `Failed to ${
								editingProject ? 'update' : 'create'
						  } project. Please try again.`,
				type: 'error'
			});
		}
	};

	const handleCancelForm = () => {
		setShowForm(false);
		setEditingProject(null);
	};

	const handleReorder = useCallback((newProjects: IProject[]) => {
		setProjects(newProjects);
		setHasOrderChanged(true);
	}, []);

	const handleSaveOrder = async () => {
		try {
			const updatedProjects = projects.map((project, index) => ({
				...project,
				order: index
			}));

			await Promise.all(
				updatedProjects.map((project) =>
					fetch(`/api/projects/${project._id}`, {
						method: 'PATCH',
						headers: { 'Content-Type': 'application/json' },
						body: JSON.stringify({ order: project.order }),
						credentials: 'include'
					})
				)
			);

			const response = await fetch('/api/projects', {
				credentials: 'include'
			});
			if (!response.ok) {
				throw new Error('Failed to fetch updated projects');
			}
			const updatedProjectsFromServer: IProject[] = await response.json();

			const sortedProjects = updatedProjectsFromServer.sort(
				(a, b) => a.order - b.order
			);

			setProjects(sortedProjects);
			setOriginalOrder(sortedProjects);
			setHasOrderChanged(false);
			toast({
				title: 'Success',
				description: 'Project order updated successfully',
				type: 'success'
			});
		} catch (error) {
			console.error('Error updating project order:', error);
			toast({
				title: 'Error',
				description: 'Failed to update project order. Please try again.',
				type: 'error'
			});
		}
	};

	const handleCancelReorder = () => {
		setProjects(originalOrder);
		setHasOrderChanged(false);
	};



	return (
		<DndProvider backend={HTML5Backend}>
			<div className="container mx-auto px-4 py-8">
				<div className="flex justify-between items-center mb-4">
					<h1 className="text-2xl font-bold">Your Projects</h1>
					{!showForm && (
						<Button onClick={() => setShowForm(true)}>
							Create New Project
						</Button>
					)}
				</div>
				{showForm ? (
					<div className="mt-8">
						<h2 className="text-xl font-semibold mb-4">
							{editingProject ? 'Edit Project' : 'Create New Project'}
						</h2>
						<ProjectForm
							initialData={editingProject}
							onSubmit={handleCreateOrUpdateProject}
							onCancel={handleCancelForm}
						/>
					</div>
				) : (
					<>
						{isMobile ? (
							<Select
								onValueChange={(value) => setActiveTab(value as StatusFilter)}
								defaultValue={activeTab}
							>
								<SelectTrigger className="w-full mb-4">
									<SelectValue placeholder="Select status" />
								</SelectTrigger>
								<SelectContent>
									{Object.values(StatusFilter).map((status) => (
										<SelectItem key={status} value={status}>
											{status} Projects
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						) : (
							<Tabs
								value={activeTab}
								onValueChange={(value) => setActiveTab(value as StatusFilter)}
							>
								<TabsList className="flex flex-col sm:flex-row">
									{Object.values(StatusFilter).map((status) => (
										<TabsTrigger
											key={status}
											value={status}
											className="flex-grow"
										>
											{status} Projects
										</TabsTrigger>
									))}
								</TabsList>
							</Tabs>
						)}
						<div className="mt-4">
							{isLoading ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
									{[...Array(6)].map((_, i) => (
										<Card key={i} className="flex flex-col w-full relative animate-pulse">
											<CardHeader>
												<div className="flex justify-between items-start">
													<div className="flex items-center gap-2">
														<div className="w-3 h-3 rounded-full bg-muted"></div>
														<div className="h-6 bg-muted rounded w-3/4"></div>
													</div>
													<div className="h-6 bg-muted rounded w-20"></div>
												</div>
											</CardHeader>
											<CardContent className="flex-grow">
												<div className="relative w-full h-48 mb-4 bg-muted rounded-md"></div>
												<div className="mt-2 mb-3">
													<div className="h-4 bg-muted rounded w-full mb-2"></div>
													<div className="h-4 bg-muted rounded w-5/6"></div>
												</div>
												<div className="h-6 bg-muted rounded w-1/4 mt-2"></div>
											</CardContent>
											<div className="p-4 bg-muted/20 flex justify-between items-center">
												<div className="flex space-x-2">
													<div className="h-10 bg-muted rounded w-16"></div>
													<div className="h-10 bg-muted rounded w-20"></div>
												</div>
											</div>
										</Card>
									))}
								</div>
							) : error ? (
								<Alert>
									<AlertDescription>{error}</AlertDescription>
								</Alert>
							) : (
								<>
									{projects.length === 0 ? (
										<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
											{[1, 2, 3].map((i) => (
												<Card key={i} className="flex flex-col relative">
													<CardHeader>
														<div className="text-lg font-semibold">No Projects Available</div>
													</CardHeader>
													<CardContent className="flex-grow">
														<div className="relative w-full h-40 mb-4 bg-muted flex items-center justify-center rounded-md">
															<span className="text-muted-foreground">No image available</span>
														</div>
														<div className="mt-2 mb-3 text-sm text-muted-foreground">
															No projects available. Create a new project to get started.
														</div>
													</CardContent>
													<div className="p-4 bg-muted/20 flex justify-center">
														<Button onClick={() => setShowForm(true)}>Create New Project</Button>
													</div>
												</Card>
											))}
										</div>
									) : (
										<ProjectList
											projects={activeTab === StatusFilter.All
												? projects
												: projects.filter((project) => project.status === activeTab as unknown as Status)
											}
											onEdit={handleEdit}
											onDelete={handleDelete}
											onChangeStatus={handleChangeStatus}
											onToggleFeatured={handleToggleFeatured}
											onReorder={handleReorder}
											isMobile={isMobile}
										/>
									)}
								</>
							)}
						</div>
						{hasOrderChanged && (
							<div className="mt-4 flex justify-end space-x-2">
								<Button onClick={handleCancelReorder} variant="outline">
									Cancel
								</Button>
								<Button onClick={handleSaveOrder}>Save Order</Button>
							</div>
						)}
					</>
				)}
			</div>
		</DndProvider>
	);
}
