import { NextResponse } from 'next/server';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { connectToDatabase } from '@/lib/mongoose';
import User from '@/models/User';

export async function POST(request: Request) {
  try {
    // Await authOptions to properly handle cookies and headers
    const options = await authOptions();
    const session = await getServerSession(options);
    if (!session) {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
    }

    await connectToDatabase();
    const { username } = await request.json();

    const updatedUser = await User.findByIdAndUpdate(
      session.user.id,
      { name: username },
      { new: true }
    );

    if (!updatedUser) {
      return NextResponse.json({ message: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({ message: 'Profile updated successfully' });
  } catch (error) {
    console.error('Profile update error:', error);
    return NextResponse.json({ message: 'An error occurred while updating the profile' }, { status: 500 });
  }
}