import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongodb';
import Project from '@/models/Project';
import Product from '@/models/Product';
import { deleteFile } from '@/lib/minio';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

// DELETE /api/webgl/:id
export async function DELETE(
  req: Request,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);

    // Check if user is authenticated and is an admin
    if (!session || !session.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id } = params;
    if (!id) {
      return NextResponse.json(
        { error: 'Media ID is required' },
        { status: 400 }
      );
    }

    // Connect to the database
    await connectToDatabase();

    // Find the media item in projects
    let project = await Project.findOne({ 'media._id': id });
    let product = null;

    // If not found in projects, check products
    if (!project) {
      product = await Product.findOne({ 'media._id': id });
    }

    // If media not found in either, return error
    if (!project && !product) {
      return NextResponse.json(
        { error: 'WebGL build not found' },
        { status: 404 }
      );
    }

    // Get the media item
    const item = project || product;
    const mediaItem = item.media.find((m: any) => m._id.toString() === id);

    // Check if it's a WebGL item
    if (!mediaItem || mediaItem.type !== 'webgl') {
      return NextResponse.json(
        { error: 'Media item is not a WebGL build' },
        { status: 400 }
      );
    }

    // Delete all associated files from MinIO
    const filesToDelete = [
      mediaItem.loaderUrl,
      mediaItem.dataUrl,
      mediaItem.frameworkUrl,
      mediaItem.codeUrl
    ];

    // Add optional files if they exist
    if (mediaItem.memoryUrl) filesToDelete.push(mediaItem.memoryUrl);
    if (mediaItem.backgroundUrl) filesToDelete.push(mediaItem.backgroundUrl);

    // Delete files from MinIO
    const deletePromises = filesToDelete.map(async (url) => {
      if (!url) return;

      try {
        // The deleteFile function will handle extracting the bucket and key from the URL
        await deleteFile(undefined, url);
      } catch (error) {
        console.error(`Error deleting file ${url}:`, error);
      }
    });

    await Promise.all(deletePromises);

    // Remove the media item from the project/product
    if (project) {
      project.media = project.media.filter((m: any) => m._id.toString() !== id);

      // If this was the WebGL demo, reset the webglDemo settings
      if (project.webglDemo && project.webglDemo.mediaId === id) {
        project.webglDemo.enabled = false;
        project.webglDemo.mediaId = '';
      }

      await project.save();
    } else if (product) {
      product.media = product.media.filter((m: any) => m._id.toString() !== id);

      // If this was the WebGL demo, reset the webglDemo settings
      if (product.webglDemo && product.webglDemo.mediaId === id) {
        product.webglDemo.enabled = false;
        product.webglDemo.mediaId = '';
      }

      await product.save();
    }

    return NextResponse.json(
      { success: true, message: 'WebGL build deleted successfully' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error deleting WebGL build:', error);
    return NextResponse.json(
      { error: 'Failed to delete WebGL build' },
      { status: 500 }
    );
  }
}
