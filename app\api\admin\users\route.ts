export const dynamic = 'force-dynamic'

import { NextResponse } from 'next/server'
import { connectToDatabase } from '@/lib/mongoose'
import User from '@/models/User'
import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"

export async function GET(request: Request) {
  try {
    const session = await getServerSession(await authOptions())
    if (!session || session.user.role !== 'admin') {
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })
    }

    await connectToDatabase()
    const users = await User.find({}, '-password') // Exclude password field

    return NextResponse.json({ users }, { status: 200 })
  } catch (error) {
    console.error('Fetch users error:', error)
    return NextResponse.json({ message: 'An error occurred while fetching users' }, { status: 500 })
  }
}