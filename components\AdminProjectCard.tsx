import React from 'react';
import {
	<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>
} from '@nextui-org/react';
import { IProject } from '@/models/Project';

interface AdminProjectCardProps {
	project: IProject;
	onEdit: (id: string) => void;
	onArchive: (id: string) => void;
}

export function AdminProjectCard({
	project,
	onEdit,
	onArchive
}: AdminProjectCardProps) {
	return (
		<Card className="w-full">
			<CardHeader className="flex justify-between items-start">
				<h3 className="text-lg font-semibold">{project.title}</h3>
				<div className="flex flex-wrap gap-2">
					<span className="px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
						{project.status}
					</span>
					{project.isFeatured && (
						<span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
							Featured
						</span>
					)}
				</div>
			</<PERSON><PERSON><PERSON>er>
			<CardBody>
				<p className="text-sm font-semibold mt-2">
					Price: {Number(project.price) === 0 ? 'Free' : `$${project.price}`}
				</p>
			</CardBody>
			<CardFooter className="flex justify-between">
				<Button
					size="sm"
					color="primary"
					onClick={() => onEdit(project._id.toString())}
				>
					Edit
				</Button>
				<Button
					size="sm"
					color="danger"
					onClick={() => onArchive(project._id.toString())}
				>
					Archive
				</Button>
			</CardFooter>
		</Card>
	);
}
