'use client';

import { useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button, Input, Card, CardBody, CardHeader } from '@nextui-org/react';

export default function ResetPassword() {
	const [password, setPassword] = useState('');
	const [confirmPassword, setConfirmPassword] = useState('');
	const [message, setMessage] = useState('');
	const searchParams = useSearchParams();
	const token = searchParams?.get('token') || '';

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (password !== confirmPassword) {
			setMessage('Passwords do not match');
			return;
		}

		try {
			const response = await fetch('/api/reset-password', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ token, password })
			});

			const data = await response.json();

			if (response.ok) {
				setMessage('Password reset successfully');
			} else {
				setMessage(data.error || 'Failed to reset password');
			}
		} catch (error) {
			setMessage('An error occurred. Please try again.');
		}
	};

	return (
		<div className="flex justify-center items-center min-h-screen">
			<Card className="w-full max-w-md">
				<CardHeader className="flex justify-center">
					<h1 className="text-2xl font-bold">Reset Password</h1>
				</CardHeader>
				<CardBody>
					<form onSubmit={handleSubmit} className="space-y-4">
						<Input
							type="password"
							label="New Password"
							value={password}
							onChange={(e) => setPassword(e.target.value)}
							required
						/>
						<Input
							type="password"
							label="Confirm New Password"
							value={confirmPassword}
							onChange={(e) => setConfirmPassword(e.target.value)}
							required
						/>
						<Button type="submit" color="primary">
							Reset Password
						</Button>
					</form>
					{message && <p className="mt-4 text-center">{message}</p>}
				</CardBody>
			</Card>
		</div>
	);
}
