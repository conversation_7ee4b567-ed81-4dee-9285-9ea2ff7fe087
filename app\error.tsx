'use client'

import { useEffect } from 'react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    console.error('Application error:', error)
  }, [error])

  // Detect specific error types
  const isWebSocketError =
    error?.message?.includes('WebSocket') ||
    error?.message?.includes('Failed to fetch') ||
    error?.stack?.includes('WebSocket');

  const isModuleError =
    error?.message?.includes('Cannot find module') ||
    error?.message?.includes('MODULE_NOT_FOUND');

  // Choose appropriate alert variant and title based on error type
  const alertVariant = isWebSocketError ? "default" : "destructive";
  const alertTitle =
    isWebSocketError ? "Connection Error" :
    isModuleError ? "Module Loading Error" :
    "Something went wrong!";

  return (
    <div className="container mx-auto px-4 py-8">
      <Alert variant={alertVariant as "default" | "destructive"}>
        <AlertTitle>{alertTitle}</AlertTitle>
        <AlertDescription>
          {isWebSocketError ? (
            <p className="mb-4">There was a problem with the development server connection. This is likely a temporary issue.</p>
          ) : isModuleError ? (
            <>
              <p className="mb-2">There was a problem loading a required module. This might be due to:</p>
              <ul className="list-disc ml-6 mb-4">
                <li>Incomplete or corrupted installation</li>
                <li>Missing dependencies</li>
                <li>Incompatible module versions</li>
              </ul>
            </>
          ) : (
            <p className="mb-4">We apologize for the inconvenience. An unexpected error occurred while loading the page.</p>
          )}

          {!isWebSocketError && !isModuleError && error.digest && (
            <p className="mb-4">Error ID: {error.digest}</p>
          )}

          <div className="flex flex-wrap gap-3">
            <Button onClick={() => reset()}>Try again</Button>

            <Button
              onClick={() => window.location.reload()}
              variant="outline"
            >
              Reload Page
            </Button>

            {isModuleError && (
              <Button
                onClick={() => window.location.href = '/'}
                variant="secondary"
              >
                Go to Homepage
              </Button>
            )}
          </div>
        </AlertDescription>
      </Alert>
    </div>
  )
}