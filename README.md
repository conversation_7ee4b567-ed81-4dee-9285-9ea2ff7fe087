# RealSoft Games Portfolio

## Build Steps

1. Install dependencies and build the Next.js application:
```bash
# Install dependencies
npm install

# Build the Next.js application
npm run build
```

2. Build the Docker image:
```bash
# Build with proper tags
docker build -t realsoftgamesportfolio:latest .
```

3. Tag for private registry:
```bash
# Tag for your private registry
docker tag realsoftgamesportfolio:latest *************:6500/realsoftgamesportfolio:latest
```

4. Push to private registry:
```bash
# Push to private registry
docker push *************:6500/realsoftgamesportfolio:latest
```

## Architecture

This application is built with Next.js using the App Router architecture:

- **Frontend**: React with Next.js App Router
- **Backend**: Next.js API Routes
- **Database**: MongoDB
- **Storage**: MinIO for media files
- **Authentication**: NextAuth.js
- **Styling**: Tailwind CSS with shadcn/ui components
- **Containerization**: Docker

## Important Notes

- Ensure your private registry (192.168.0.59:6500) is accessible
- The build machine must have Docker installed and configured
- Node.js 23.x is used for the build and runtime processes
- Make sure the realsoftgames volume exists on the target server
- The application requires MongoDB for data storage
- MinIO is used for image storage instead of local file system
- Environment variables must be properly configured (see .env.example)

## Environment Configuration

Copy the .env.example file to .env and update the values:

```bash
# Copy example environment file
cp .env.example .env

# Edit the file with your configuration
nano .env
```

Key environment variables:
- `MONGODB_URI`: MongoDB connection string
- `NEXTAUTH_SECRET`: Secret for NextAuth authentication
- `MINIO_ENDPOINT`, `MINIO_ACCESS_KEY`, etc.: MinIO configuration

## Security Features

The application includes several security features:

1. **Non-root Docker User**: The application runs as a non-root user in Docker for improved security
2. **MongoDB Serialization**: All MongoDB objects are properly serialized to prevent data leakage
3. **Environment Variable Protection**: Sensitive environment variables are not exposed to the client
4. **API Token Authentication**: API endpoints are protected with token-based authentication
5. **Input Validation**: All user inputs are validated before processing

## Error Handling

The application uses a comprehensive error handling system:

1. **Global Error Boundary**: Catches and displays errors in a user-friendly way
2. **Specific Error Types**: Handles different types of errors (connection, module loading, etc.)
3. **Contextual Error Messages**: Provides relevant error messages based on the error type

To use the error boundary in your components:

```tsx
import ErrorBoundary from '@/components/error-boundary';

// Basic usage
<ErrorBoundary>
  <YourComponent />
</ErrorBoundary>

// With component name for better error reporting
<ErrorBoundary componentName="ComponentName">
  <YourComponent />
</ErrorBoundary>

// With custom fallback UI
<ErrorBoundary fallback={<CustomErrorUI />}>
  <YourComponent />
</ErrorBoundary>
```

## Troubleshooting

### If build fails:
1. Clear build cache: `rm -rf .next`
2. Delete node_modules: `rm -rf node_modules`
3. Clean npm cache: `npm cache clean --force`
4. Retry build process from step 1

### If Next.js development server has issues:
1. Run the fix script: `./fix-nextjs.bat` (Windows) or `./fix-nextjs.ps1` (PowerShell)
2. If you encounter module errors like `Cannot find module '../server/lib/trace/tracer'`:
   - Delete node_modules and reinstall: `rm -rf node_modules && npm install`
   - Reinstall the specific Next.js version: `npm install next@13.4.12 --save`
3. If you see WebSocket connection errors or `Failed to fetch` errors:
   - Restart the development server
   - Check if another process is using port 3000
   - Try clearing browser cache

### If image push fails:
1. Verify registry connection: `curl http://*************:6500/v2/_catalog`
2. Ensure Docker is logged into private registry
3. Check registry permissions

### If application fails to start:
1. Verify MongoDB connection
2. Check MinIO configuration and accessibility
3. Ensure all required environment variables are set

### If API Tokens page has issues:
1. Check that the MongoDB connection is working
2. Verify that the API endpoint `/api/api-tokens` is accessible
3. Check browser console for specific error messages
