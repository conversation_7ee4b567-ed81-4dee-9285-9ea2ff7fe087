# RealSoft Games Portfolio

A modern portfolio application for game developers built with Next.js, featuring project and product management, MinIO storage integration, and comprehensive admin dashboard.

## Quick Start

### Prerequisites
- Node.js 23.x or higher
- Docker and Docker Compose
- Access to MongoDB database
- MinIO storage server
- SMTP email configuration (Gmail)

## Build and Deployment Steps

1. **Install dependencies and build the Next.js application:**
```bash
# Install dependencies
npm install

# Build the Next.js application
npm run build
```

2. **Build the Docker image:**
```bash
# Build with proper tags
docker build -t realsoftgamesportfolio:latest .
```

3. **Tag for private registry:**
```bash
# Tag for your private registry (corrected IP)
docker tag realsoftgamesportfolio:latest *************:6500/realsoftgamesportfolio:latest
```

4. **Push to private registry:**
```bash
# Push to private registry
docker push *************:6500/realsoftgamesportfolio:latest
```

## Architecture

This application is built with Next.js using the App Router architecture:

- **Frontend**: React with Next.js App Router
- **Backend**: Next.js API Routes
- **Database**: MongoDB (gamedevportfolio database)
- **Storage**: MinIO for media files and WebGL builds
- **Authentication**: NextAuth.js with custom user management
- **Styling**: Tailwind CSS with shadcn/ui components
- **Rich Text**: React-Quill for project/product descriptions
- **Containerization**: Docker with multi-stage builds
- **Email**: Nodemailer with Gmail SMTP

## Important Notes

- **Registry**: Private registry at *************:6500 must be accessible
- **Build Environment**: Docker with Node.js 23-slim base image
- **Database**: Uses MongoDB at *************:27017 with gamedevportfolio database
- **Storage**: MinIO server at *************:9000 with realsoftgames bucket
- **Network**: Application runs on port 3060 in production
- **Security**: Runs as non-root user (nextjs:nodejs) in Docker container
- **Environment**: All sensitive configuration in .env file (never commit this file)

## Environment Configuration

Copy the .env.example file to .env and update the values:

```bash
# Copy example environment file
cp .env.example .env

# Edit the file with your configuration
nano .env
```

### Required Environment Variables:

**Database Configuration:**
- `MONGODB_URI`: MongoDB connection string with authentication
  - Format: `********************************:port/database?authSource=admin&directConnection=true`
  - Example: `*****************************************************************************************************`

**Authentication:**
- `NEXTAUTH_SECRET`: Secret for NextAuth authentication (generate with `openssl rand -hex 32`)
- `NEXTAUTH_URL`: Base URL for NextAuth callbacks (http://localhost:3000 for development)

**Application URLs:**
- `NEXT_PUBLIC_APP_URL`: Public URL where the application is accessible
  - Example: `"http://*************:3060"`

**Email Configuration (Gmail SMTP):**
- `SMTP_HOST`: Gmail SMTP server (smtp.gmail.com)
- `SMTP_PORT`: Gmail SMTP port (587)
- `GMAIL_USER`: Your Gmail address
- `GMAIL_PASS`: Gmail app password (not your regular password)

**MinIO Storage Configuration:**
- `MINIO_ENDPOINT`: Internal MinIO server endpoint (*************:9000)
- `MINIO_PUBLIC_URL`: Public MinIO URL for external access (https://minioapi.realsoftgames.com)
- `MINIO_ACCESS_KEY`: MinIO access key
- `MINIO_SECRET_KEY`: MinIO secret key
- `MINIO_BUCKET`: MinIO bucket name (realsoftgames)
- `MINIO_REGION`: MinIO region (us-east-1)
- `MINIO_PATH_STYLE`: Use path-style URLs (true)

## Features

### Core Functionality
- **Project Management**: Create, edit, and manage game development projects
- **Product Management**: Showcase Unity assets and products with rich descriptions
- **Media Management**: Upload and organize images through MinIO integration
- **WebGL Builds**: Upload and host Unity WebGL builds with embedded player
- **User Authentication**: Secure login system with admin/user roles
- **Admin Dashboard**: Comprehensive management interface for all content
- **API System**: RESTful API with token-based authentication
- **Contact System**: Contact form with Discord integration and email notifications

### Technical Features
- **Responsive Design**: Mobile-first design with dark/light theme support
- **Image Optimization**: Next.js Image component with MinIO backend
- **Rich Text Editing**: React-Quill integration for detailed descriptions
- **Error Handling**: Comprehensive error boundaries and user feedback
- **Security**: Non-root Docker containers, input validation, secure headers
- **Performance**: Optimized builds with standalone Next.js output

## Deployment Configuration

### Portainer Template
The application is designed to work with Portainer using the following configuration:
- **Port**: 3060 (external) → 3000 (internal)
- **MongoDB**: *************:27017
- **MinIO**: *************:9000
- **Registry**: *************:6500/realsoftgamesportfolio:latest
- **Volume**: realsoftgames (for persistent data if needed)

### Network Configuration
- **Internal Network**: Uses direct IP addresses for database and storage connections
- **External Access**: Configured for public access through domain/IP
- **CORS**: Properly configured for cross-origin requests
- **Proxy**: Image proxy for secure MinIO access

## Security Features

The application includes several security features:

1. **Non-root Docker User**: The application runs as a non-root user in Docker for improved security
2. **MongoDB Serialization**: All MongoDB objects are properly serialized to prevent data leakage
3. **Environment Variable Protection**: Sensitive environment variables are not exposed to the client
4. **API Token Authentication**: API endpoints are protected with token-based authentication
5. **Input Validation**: All user inputs are validated before processing

## Error Handling

The application uses a comprehensive error handling system:

1. **Global Error Boundary**: Catches and displays errors in a user-friendly way
2. **Specific Error Types**: Handles different types of errors (connection, module loading, etc.)
3. **Contextual Error Messages**: Provides relevant error messages based on the error type

To use the error boundary in your components:

```tsx
import ErrorBoundary from '@/components/error-boundary';

// Basic usage
<ErrorBoundary>
  <YourComponent />
</ErrorBoundary>

// With component name for better error reporting
<ErrorBoundary componentName="ComponentName">
  <YourComponent />
</ErrorBoundary>

// With custom fallback UI
<ErrorBoundary fallback={<CustomErrorUI />}>
  <YourComponent />
</ErrorBoundary>
```

## Troubleshooting

### If build fails:
1. Clear build cache: `rm -rf .next`
2. Delete node_modules: `rm -rf node_modules`
3. Clean npm cache: `npm cache clean --force`
4. Retry build process from step 1

### If Next.js development server has issues:
1. Run the fix script: `./fix-nextjs.bat` (Windows) or `./fix-nextjs.ps1` (PowerShell)
2. If you encounter module errors like `Cannot find module '../server/lib/trace/tracer'`:
   - Delete node_modules and reinstall: `rm -rf node_modules && npm install`
   - Reinstall the specific Next.js version: `npm install next@13.4.12 --save`
3. If you see WebSocket connection errors or `Failed to fetch` errors:
   - Restart the development server
   - Check if another process is using port 3000
   - Try clearing browser cache

### If image push fails:
1. Verify registry connection: `curl http://*************:6500/v2/_catalog`
2. Ensure Docker is logged into private registry
3. Check registry permissions

### If application fails to start:
1. Verify MongoDB connection
2. Check MinIO configuration and accessibility
3. Ensure all required environment variables are set

### If API Tokens page has issues:
1. Check that the MongoDB connection is working
2. Verify that the API endpoint `/api/api-tokens` is accessible
3. Check browser console for specific error messages
