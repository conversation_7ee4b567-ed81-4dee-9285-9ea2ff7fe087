import { NextResponse } from 'next/server';
import { uploadFile, testMinioConnection } from '@/lib/minio';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
// @ts-ignore - adm-zip doesn't have type definitions
import * as AdmZip from 'adm-zip';
// @ts-ignore - uuid doesn't have type definitions
import { v4 as uuidv4 } from 'uuid';

// Function to create a temporary directory
function createTempDir() {
  const tempDir = path.join(os.tmpdir(), `webgl-${uuidv4()}`);
  fs.mkdirSync(tempDir, { recursive: true });
  return tempDir;
}

// Function to clean up temporary directory
function cleanupTempDir(tempDir: string) {
  try {
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  } catch (error) {
    console.error('Error cleaning up temp directory:', error);
  }
}

// Function to find files in the extracted directory
function findFiles(dir: string, extensions: Record<string, string>) {
  const foundFiles: Record<string, string> = {};

  function searchDirectory(currentDir: string) {
    const files = fs.readdirSync(currentDir);

    for (const file of files) {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        searchDirectory(filePath);
      } else {
        // Check if this file matches any of our target extensions
        for (const [key, ext] of Object.entries(extensions)) {
          if (file.toLowerCase().includes(ext)) {
            foundFiles[key] = filePath;
            break;
          }
        }
      }
    }
  }

  searchDirectory(dir);
  return foundFiles;
}

// Function to find an image file in a directory
function findImageInDirectory(dir: string, extensions: string[]): string | null {
  function searchDirectory(currentDir: string): string | null {
    const files = fs.readdirSync(currentDir);

    for (const file of files) {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        const found = searchDirectory(filePath);
        if (found) return found;
      } else {
        const ext = path.extname(file).toLowerCase();
        if (extensions.includes(ext)) {
          return filePath;
        }
      }
    }

    return null;
  }

  return searchDirectory(dir);
}

// Function to find a file by exact name
function findFileByName(dir: string, fileName: string): string | null {
  function searchDirectory(currentDir: string): string | null {
    const files = fs.readdirSync(currentDir);

    for (const file of files) {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        const found = searchDirectory(filePath);
        if (found) return found;
      } else if (file.toLowerCase() === fileName.toLowerCase()) {
        return filePath;
      }
    }

    return null;
  }

  return searchDirectory(dir);
}

// Function to upload a directory recursively
async function uploadDirectory(sourceDir: string, targetPrefix: string): Promise<void> {
  async function processDirectory(currentDir: string, currentPrefix: string) {
    const files = fs.readdirSync(currentDir);

    for (const file of files) {
      const filePath = path.join(currentDir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        // Create new prefix for subdirectory
        const newPrefix = `${currentPrefix}/${file}`;
        await processDirectory(filePath, newPrefix);
      } else {
        // Upload file
        const fileContent = fs.readFileSync(filePath);
        const objectKey = `${currentPrefix}/${file}`;

        // Determine content type
        let contentType = 'application/octet-stream';
        const ext = path.extname(file).toLowerCase();
        if (ext === '.html' || ext === '.htm') contentType = 'text/html';
        else if (ext === '.css') contentType = 'text/css';
        else if (ext === '.js') contentType = 'application/javascript';
        else if (ext === '.png') contentType = 'image/png';
        else if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg';
        else if (ext === '.gif') contentType = 'image/gif';
        else if (ext === '.svg') contentType = 'image/svg+xml';
        else if (ext === '.ico') contentType = 'image/x-icon';

        console.log(`Uploading ${file} to ${objectKey} (${contentType})`);
        await uploadFile(fileContent, objectKey, contentType);
      }
    }
  }

  await processDirectory(sourceDir, targetPrefix);
}

export async function POST(req: Request) {
  let tempDir = '';
  let zipPath = '';

  try {
    // Test MinIO connection before proceeding
    console.log('Testing MinIO connection before upload...');
    const isConnected = await testMinioConnection();
    if (!isConnected) {
      console.error('MinIO connection test failed. Cannot proceed with upload.');
      return NextResponse.json({
        error: 'Failed to connect to MinIO storage server. Please try again later.'
      }, { status: 500 });
    }
    console.log('MinIO connection test successful. Proceeding with upload...');
    console.log('Parsing form data...');
    const formData = await req.formData();
    const zipFile = formData.get('file') as File | null;

    console.log('Parsing URL parameters...');
    const { searchParams } = new URL(req.url);
    const folder = searchParams.get('folder');
    const itemId = searchParams.get('itemId');
    const itemType = searchParams.get('itemType');

    console.log(`Request parameters: folder=${folder}, itemId=${itemId}, itemType=${itemType}`);

    // Get metadata from form data
    const title = formData.get('title') as string;
    const width = parseInt(formData.get('width') as string) || 960;
    const height = parseInt(formData.get('height') as string) || 540;
    const demoUrl = formData.get('demoUrl') as string || '';

    console.log(`Form data: title=${title}, width=${width}, height=${height}, demoUrl=${demoUrl || 'none'}`);
    console.log(`ZIP file: ${zipFile ? zipFile.name + ' (' + zipFile.size + ' bytes)' : 'none'}`);


    if (!zipFile) {
      return NextResponse.json({ error: 'No ZIP file provided' }, { status: 400 });
    }

    if (!folder) {
      return NextResponse.json({ error: 'No folder specified' }, { status: 400 });
    }

    // Determine the base folder path - organize by webgl/itemType/itemId
    const baseFolderPath = itemId && itemType
      ? `webgl/${itemType}/${itemId}/${folder}`
      : `webgl/uploads/${folder}`;

    // Create a temporary directory
    console.log('Creating temporary directory...');
    tempDir = createTempDir();
    console.log(`Temporary directory created: ${tempDir}`);

    // Save the ZIP file to the temporary directory
    try {
      console.log('Converting ZIP file to buffer...');
      const zipBuffer = Buffer.from(await zipFile.arrayBuffer());
      zipPath = path.join(tempDir, 'build.zip');
      console.log(`Saving ZIP file to: ${zipPath}`);

      // Convert Buffer to Uint8Array to fix TypeScript error
      fs.writeFileSync(zipPath, new Uint8Array(zipBuffer));
      console.log('ZIP file saved successfully');
    } catch (error) {
      console.error('Error saving ZIP file:', error);
      throw new Error(`Failed to save ZIP file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Extract the ZIP file
    try {
      console.log(`Extracting ZIP file: ${zipPath} to ${tempDir}`);
      const zip = new AdmZip(zipPath);
      zip.extractAllTo(tempDir, true);
      console.log('ZIP file extracted successfully');
    } catch (error) {
      console.error('Error extracting ZIP file:', error);
      throw new Error(`Failed to extract ZIP file: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Find the required files - updated for modern Unity WebGL builds
    const fileExtensions = {
      'loader': '.loader.js',
      'data': '.data',
      'framework': '.framework.js',
      'wasm': '.wasm',
      // Modern Unity builds don't use .mem files anymore
    };

    console.log('Searching for WebGL files in extracted directory...');
    const foundFiles = findFiles(tempDir, fileExtensions);

    // Log what files were found
    console.log('Found WebGL files:');
    for (const [key, filePath] of Object.entries(foundFiles)) {
      console.log(`- ${key}: ${filePath ? path.basename(filePath) : 'Not found'}`);
    }

    // Validate that we found the required files
    if (!foundFiles.loader || !foundFiles.data || !foundFiles.framework || !foundFiles.wasm) {
      return NextResponse.json({
        error: 'Missing required files in ZIP. Please ensure your WebGL build contains .loader.js, .data, .framework.js, and .wasm files.'
      }, { status: 400 });
    }

    // Also check for index.html which is required for WebGL builds
    const indexHtmlPath = findFileByName(tempDir, 'index.html');
    if (!indexHtmlPath) {
      return NextResponse.json({
        error: 'Missing index.html file in ZIP. Please ensure your WebGL build contains the index.html file.'
      }, { status: 400 });
    }

    // Upload each file to MinIO
    const uploadedUrls: Record<string, string> = {};

    for (const [key, filePath] of Object.entries(foundFiles)) {
      if (!filePath) continue;

      try {
        console.log(`Processing ${key} file: ${filePath}`);
        const fileContent = fs.readFileSync(filePath);
        const fileName = path.basename(filePath);
        const objectKey = `${baseFolderPath}/${key}_${Date.now()}_${fileName}`;

        // Determine content type
        let contentType = 'application/octet-stream';
        if (fileName.endsWith('.js')) contentType = 'application/javascript';
        else if (fileName.endsWith('.wasm')) contentType = 'application/wasm';
        else if (fileName.endsWith('.data')) contentType = 'application/octet-stream';
        else if (fileName.endsWith('.mem')) contentType = 'application/octet-stream';

        console.log(`Uploading ${key} file to MinIO: ${objectKey} (${contentType})`);

        // Upload to MinIO
        const url = await uploadFile(fileContent, objectKey, contentType);
        console.log(`Successfully uploaded ${key} file: ${url}`);
        uploadedUrls[`${key}Url`] = url;
      } catch (error) {
        console.error(`Error uploading ${key} file:`, error);
        throw new Error(`Failed to upload ${key} file: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    // Look for a background image (any image file in the directory)
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];

    // Define findImage outside the block to fix TypeScript error
    const backgroundImage = findImageInDirectory(tempDir, imageExtensions);
    if (backgroundImage) {
      try {
        console.log(`Processing background image: ${backgroundImage}`);
        const fileContent = fs.readFileSync(backgroundImage);
        const fileName = path.basename(backgroundImage);
        const objectKey = `${baseFolderPath}/background_${Date.now()}_${fileName}`;

        // Determine content type
        const ext = path.extname(fileName).toLowerCase();
        let contentType = 'image/jpeg'; // Default
        if (ext === '.png') contentType = 'image/png';
        else if (ext === '.gif') contentType = 'image/gif';
        else if (ext === '.webp') contentType = 'image/webp';

        console.log(`Uploading background image to MinIO: ${objectKey} (${contentType})`);

        // Upload to MinIO
        const url = await uploadFile(fileContent, objectKey, contentType);
        console.log(`Successfully uploaded background image: ${url}`);
        uploadedUrls.backgroundUrl = url;
      } catch (error) {
        // Don't fail the whole upload if the background image fails
        console.error('Error uploading background image:', error);
        // Continue without the background image
      }
    }

    // Upload index.html file
    let indexHtmlUrl = '';
    try {
      console.log(`Processing index.html file: ${indexHtmlPath}`);
      const fileContent = fs.readFileSync(indexHtmlPath!);
      const objectKey = `${baseFolderPath}/index.html`;

      console.log(`Uploading index.html to MinIO: ${objectKey}`);

      // Upload to MinIO
      indexHtmlUrl = await uploadFile(fileContent, objectKey, 'text/html');
      console.log(`Successfully uploaded index.html: ${indexHtmlUrl}`);
    } catch (error) {
      console.error('Error uploading index.html:', error);
      throw new Error(`Failed to upload index.html: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }

    // Upload TemplateData folder if it exists
    const templateDataDir = path.join(tempDir, 'TemplateData');
    if (fs.existsSync(templateDataDir)) {
      try {
        console.log('Uploading TemplateData files...');
        await uploadDirectory(templateDataDir, `${baseFolderPath}/TemplateData`);
      } catch (error) {
        console.error('Error uploading TemplateData:', error);
        // Continue without TemplateData
      }
    }

    // Create the response with all the WebGL build information
    const response = {
      type: 'webgl',
      url: indexHtmlUrl, // Main URL is now the index.html URL
      title,
      width,
      height,
      indexUrl: indexHtmlUrl,
      loaderUrl: uploadedUrls.loaderUrl,
      dataUrl: uploadedUrls.dataUrl,
      frameworkUrl: uploadedUrls.frameworkUrl,
      wasmUrl: uploadedUrls.wasmUrl,
      backgroundUrl: uploadedUrls.backgroundUrl,
      demoUrl
    };

    return NextResponse.json(response, { status: 200 });
  } catch (error) {
    console.error('Error processing WebGL ZIP:', error);
    // Provide more detailed error information for debugging
    const errorMessage = error instanceof Error
      ? `${error.name}: ${error.message}`
      : 'Unknown error occurred';

    return NextResponse.json(
      { error: 'Failed to process WebGL ZIP file', details: errorMessage },
      { status: 500 }
    );
  } finally {
    // Clean up the temporary directory
    if (tempDir) {
      cleanupTempDir(tempDir);
    }
  }
}
