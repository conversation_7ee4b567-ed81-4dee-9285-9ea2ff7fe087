import mongoose, { Schema, Document } from 'mongoose';

export interface IAnalyticsData extends Document {
  totalUsers: number;
  totalProjects: number;
  totalProducts: number;
  userCountByCountry: Array<{ _id: string; count: number }>; 
  visitorCountByCountry: Array<{ _id: string; count: number }>; 
}

const AnalyticsDataSchema: Schema = new Schema({
  totalUsers: { type: Number, required: true },
  totalProjects: { type: Number, required: true },
  totalProducts: { type: Number, required: true },
  userCountByCountry: [{ _id: String, count: Number }],
  visitorCountByCountry: [{ _id: String, count: Number }],
}, { timestamps: true });

const AnalyticsData = mongoose.models.AnalyticsData || mongoose.model<IAnalyticsData>('AnalyticsData', AnalyticsDataSchema);

export default AnalyticsData;