import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { uploadFile, deleteFile } from '@/lib/minio';

export const runtime = 'nodejs'; // This replaces the deprecated config export
export const dynamic = 'force-dynamic';

export async function POST(req: Request) {
	try {
		const formData = await req.formData();
		const file = formData.get('file') as File | null;
		const { searchParams } = new URL(req.url);
		const type = searchParams.get('type');

		if (!file) {
			return NextResponse.json({ error: 'No file provided' }, { status: 400 });
		}

		// Get the item ID and type from query parameters
		const itemId = searchParams.get('itemId');
		const itemType = searchParams.get('itemType'); // 'projects' or 'products'
		const customFolder = searchParams.get('folder'); // For WebGL builds
		const filename = searchParams.get('filename'); // For WebGL files (loader, data, etc.)

		// Determine folder based on file type and item information
		let folder = 'images';
		if (type === 'avatar') {
			folder = 'avatars';
		} else if (type === 'webgl' && itemType && itemId && customFolder) {
			// For WebGL builds, organize by item type, ID, and custom folder
			folder = `${itemType}/${itemId}/webgl/${customFolder}`;
		} else if (type === 'webgl' && customFolder) {
			// For WebGL builds without item info
			folder = `webgl/${customFolder}`;
		} else if (itemType && itemId) {
			// If we have item information, organize by item type and ID
			folder = `${itemType}/${itemId}`;
		}

		// Create a sanitized filename
		let sanitizedFileName;
		if (type === 'webgl' && filename) {
			// For WebGL files, use the specific filename (loader, data, etc.)
			sanitizedFileName = `${filename}_${Date.now()}_${file.name.replace(/\s+/g, '_')}`;
		} else if (type === 'avatar') {
			sanitizedFileName = `avatar_${Date.now()}_${file.name.replace(/\s+/g, '_')}`;
		} else {
			sanitizedFileName = `image_${Date.now()}_${file.name.replace(/\s+/g, '_')}`;
		}

		// Create the object key (path in MinIO)
		const objectKey = `${folder}/${sanitizedFileName}`;

		// Convert file to buffer
		const arrayBuffer = await file.arrayBuffer();
		const buffer = Buffer.from(arrayBuffer);

		// Upload to MinIO
		const publicUrl = await uploadFile(buffer, objectKey, file.type);
		console.log('File uploaded to MinIO:', objectKey);
		console.log('Public URL:', publicUrl);

		return NextResponse.json({ url: publicUrl }, { status: 200 });
	} catch (error) {
		console.error('Error uploading file:', error);
		return NextResponse.json(
			{ error: 'Failed to upload file' },
			{ status: 500 }
		);
	}
}

export async function DELETE(req: Request) {
	try {
		const { searchParams } = new URL(req.url);
		const fileUrl = searchParams.get('fileUrl');

		if (!fileUrl) {
			return NextResponse.json(
				{ error: 'No file URL provided' },
				{ status: 400 }
			);
		}

		// Delete from MinIO
		await deleteFile(fileUrl);
		console.log('File deleted from MinIO:', fileUrl);

		return NextResponse.json(
			{ message: 'File deleted successfully' },
			{ status: 200 }
		);
	} catch (error) {
		console.error('Error deleting file:', error);
		return NextResponse.json(
			{ error: 'Failed to delete file' },
			{ status: 500 }
		);
	}
}
