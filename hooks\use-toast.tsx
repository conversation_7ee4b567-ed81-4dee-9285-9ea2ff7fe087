'use client';

import * as React from 'react';

type ToastVariant = 'default' | 'destructive' | 'success' | 'info' | 'warning';

type ToastActionElement = React.ReactElement<unknown>;

export interface Toast {
  id: string;
  title?: string;
  description?: React.ReactNode;
  action?: ToastActionElement;
  variant?: ToastVariant;
  duration?: number;
  // For backward compatibility
  type?: 'success' | 'error' | 'info' | 'warning';
  message?: string;
}

interface ToastContextType {
  toasts: Toast[];
  toast: (data: Omit<Toast, 'id'> | { message: string; type?: 'success' | 'error' | 'info' | 'warning' }) => void;
  dismiss: (id: string) => void;
  dismissAll: () => void;
}

const ToastContext = React.createContext<ToastContextType | null>(null);

function mapTypeToVariant(type?: 'success' | 'error' | 'info' | 'warning'): ToastVariant {
  switch (type) {
    case 'success':
      return 'success';
    case 'error':
      return 'destructive';
    case 'warning':
      return 'warning';
    case 'info':
    default:
      return 'default';
  }
}

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = React.useState<Toast[]>([]);

  const toast = React.useCallback(
    (data: Omit<Toast, 'id'> | { message: string; type?: 'success' | 'error' | 'info' | 'warning' }) => {
      const id = Math.random().toString(36).substring(2, 9);
      let newToast: Toast;

      if ('message' in data) {
        // Handle legacy format
        newToast = {
          id,
          description: data.message,
          variant: mapTypeToVariant(data.type),
          type: data.type,
          duration: 3000,
        };
      } else {
        // Handle new format
        newToast = {
          id,
          ...data,
          // Map type to variant if type is provided but variant isn't
          variant: data.variant || (data.type ? mapTypeToVariant(data.type) : 'default'),
          duration: data.duration || 3000,
        };
      }

      setToasts((prev) => [...prev, newToast]);

      // Auto dismiss
      setTimeout(() => {
        setToasts((prev) => prev.filter((toast) => toast.id !== id));
      }, newToast.duration || 3000);
    },
    []
  );

  const dismiss = React.useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  }, []);

  const dismissAll = React.useCallback(() => {
    setToasts([]);
  }, []);

  return (
    <ToastContext.Provider value={{ toasts, toast, dismiss, dismissAll }}>
      {children}
    </ToastContext.Provider>
  );
}

export function useToast() {
  const context = React.useContext(ToastContext);

  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }

  return context;
}