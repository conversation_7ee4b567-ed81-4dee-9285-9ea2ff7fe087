# Script to fix Next.js installation issues

Write-Host "Fixing Next.js installation..." -ForegroundColor Green

# Step 1: Clear Next.js cache
Write-Host "Clearing Next.js cache..." -ForegroundColor Yellow
if (Test-Path ".next") {
    Remove-Item -Recurse -Force ".next"
    Write-Host "Next.js cache cleared." -ForegroundColor Green
} else {
    Write-Host "No .next directory found. Skipping cache clear." -ForegroundColor Yellow
}

# Step 2: Delete node_modules
Write-Host "Removing node_modules..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Remove-Item -Recurse -Force "node_modules"
    Write-Host "node_modules removed." -ForegroundColor Green
} else {
    Write-Host "No node_modules directory found. Skipping removal." -ForegroundColor Yellow
}

# Step 3: Clear npm cache
Write-Host "Clearing npm cache..." -ForegroundColor Yellow
npm cache clean --force
Write-Host "npm cache cleared." -ForegroundColor Green

# Step 4: Reinstall dependencies
Write-Host "Reinstalling dependencies..." -ForegroundColor Yellow
npm install
Write-Host "Dependencies reinstalled." -ForegroundColor Green

# Step 5: Run development server
Write-Host "Starting development server..." -ForegroundColor Yellow
Write-Host "You can now run 'npm run dev' to start the development server." -ForegroundColor Green
Write-Host "If you still encounter issues, try running 'npm install next@13.4.12 --save' to reinstall the specific Next.js version." -ForegroundColor Yellow

Write-Host "Fix completed!" -ForegroundColor Green
