'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { IMedia } from '@/models/Media';
import { Gamepad2, Save, Trash2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import axios from 'axios';

interface WebGLDemoSettingsProps {
  itemId: string;
  itemType: 'projects' | 'products';
  media: IMedia[];
  initialSettings?: {
    enabled: boolean;
    demoUrl?: string;
    mediaId?: string;
  };
  onSave: (settings: {
    enabled: boolean;
    demoUrl?: string;
    mediaId?: string;
  }) => Promise<void>;
  onDelete?: (mediaId: string) => Promise<void>;
}

export default function WebGLDemoSettings({ 
  itemId, 
  itemType, 
  media, 
  initialSettings,
  onSave,
  onDelete
}: WebGLDemoSettingsProps) {
  const [enabled, setEnabled] = useState(initialSettings?.enabled || false);
  const [demoUrl, setDemoUrl] = useState(initialSettings?.demoUrl || '');
  const [selectedMediaId, setSelectedMediaId] = useState(initialSettings?.mediaId || '');
  const [isSaving, setIsSaving] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { toast } = useToast();
  
  // Filter media to only include WebGL items
  const webglMedia = media.filter(item => item.type === 'webgl');
  
  // Update state when initialSettings change
  useEffect(() => {
    if (initialSettings) {
      setEnabled(initialSettings.enabled);
      setDemoUrl(initialSettings.demoUrl || '');
      setSelectedMediaId(initialSettings.mediaId || '');
    }
  }, [initialSettings]);
  
  const handleSave = async () => {
    setIsSaving(true);
    
    try {
      await onSave({
        enabled,
        demoUrl: demoUrl || undefined,
        mediaId: selectedMediaId || undefined
      });
      
      toast({
        title: "Settings saved",
        description: "WebGL demo settings have been updated.",
        duration: 3000,
      });
    } catch (error) {
      console.error('Error saving WebGL demo settings:', error);
      toast({
        title: "Error saving settings",
        description: "An error occurred while saving WebGL demo settings.",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  const handleDelete = async () => {
    if (!selectedMediaId) return;
    
    setIsDeleting(true);
    
    try {
      if (onDelete) {
        await onDelete(selectedMediaId);
      }
      
      // Reset settings
      setEnabled(false);
      setSelectedMediaId('');
      
      toast({
        title: "WebGL build deleted",
        description: "The WebGL build has been deleted successfully.",
        duration: 3000,
      });
    } catch (error) {
      console.error('Error deleting WebGL build:', error);
      toast({
        title: "Error deleting build",
        description: "An error occurred while deleting the WebGL build.",
        variant: "destructive",
        duration: 3000,
      });
    } finally {
      setIsDeleting(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center gap-2">
          <Gamepad2 className="h-5 w-5" />
          <CardTitle>WebGL Demo Settings</CardTitle>
        </div>
        <CardDescription>
          Configure WebGL demo settings for this {itemType.slice(0, -1)}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-2">
          <Switch 
            id="demo-enabled" 
            checked={enabled}
            onCheckedChange={setEnabled}
            disabled={webglMedia.length === 0}
          />
          <Label htmlFor="demo-enabled">Enable WebGL Demo</Label>
        </div>
        
        {webglMedia.length === 0 && (
          <div className="text-sm text-muted-foreground">
            No WebGL builds available. Upload a WebGL build in the media section first.
          </div>
        )}
        
        {enabled && (
          <>
            <div className="space-y-2">
              <Label htmlFor="webgl-build">Select WebGL Build</Label>
              <Select 
                value={selectedMediaId} 
                onValueChange={setSelectedMediaId}
                disabled={webglMedia.length === 0}
              >
                <SelectTrigger id="webgl-build">
                  <SelectValue placeholder="Select a WebGL build" />
                </SelectTrigger>
                <SelectContent>
                  {webglMedia.map((item) => (
                    <SelectItem key={item._id} value={item._id || ''}>
                      {item.title || 'Untitled WebGL Build'}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="demo-url">
                External Demo URL (optional)
              </Label>
              <Input 
                id="demo-url" 
                value={demoUrl} 
                onChange={(e) => setDemoUrl(e.target.value)}
                placeholder="https://example.com/my-game"
              />
              <p className="text-xs text-muted-foreground">
                If provided, this URL will be used as an alternative link to play the game
              </p>
            </div>
          </>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button 
              variant="destructive" 
              disabled={!selectedMediaId || isDeleting}
            >
              {isDeleting ? (
                <>
                  <span className="mr-2">Deleting...</span>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete WebGL Build
                </>
              )}
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the WebGL build
                and remove it from our servers.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction onClick={handleDelete}>
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
        
        <Button 
          onClick={handleSave} 
          disabled={isSaving || (enabled && !selectedMediaId)}
        >
          {isSaving ? (
            <>
              <span className="mr-2">Saving...</span>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" />
              Save Settings
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
