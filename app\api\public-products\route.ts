import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongoose';
import Product from '@/models/Product';
import { serializeData } from '@/lib/serialization';
import { withCors } from '@/app/api/cors-middleware';

export const dynamic = 'force-dynamic';

/**
 * Public API endpoint for products that doesn't require authentication
 */
async function handler(req: NextRequest) {
  try {
    console.log('GET /api/public-products: Starting...');
    await connectToDatabase();
    console.log('Database connected');

    const { searchParams } = new URL(req.url);
    const featured = searchParams.get('featured');

    let query = {};
    if (featured === 'true') {
      query = { isFeatured: true };
    }

    console.log('Query:', query);

    const products = await Product.find(query).sort({ order: 1 }).lean();
    console.log(`Found ${products.length} products`);

    // Serialize the products to avoid MongoDB object issues
    const serializedProducts = serializeData(products);

    return NextResponse.json(serializedProducts);
  } catch (error) {
    console.error('Error in GET /api/public-products:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// Apply CORS middleware to the handler
export const GET = (req: NextRequest) => withCors(req, handler);

// Handle OPTIONS requests for CORS
export const OPTIONS = (req: NextRequest) => withCors(req, async () => new NextResponse(null, { status: 204 }));
