/** @type {import('next').NextConfig} */
const nextConfig = {
	typescript: {
		// !! WARN !!
		// Dangerously allow production builds to successfully complete even if
		// your project has type errors.
		// !! WARN !!
		ignoreBuildErrors: true,
	},
	reactStrictMode: true,
	images: {
		domains: ['localhost', 'realsoftgames.com', 'minioapi.realsoftgames.com', '************'],
		remotePatterns: [
			{
				protocol: 'http',
				hostname: 'localhost',
				port: '3000',
				pathname: '/uploads/**'
			},
			{
				protocol: 'https',
				hostname: 'realsoftgames.com',
				pathname: '/uploads/**'
			},
			{
				protocol: 'https',
				hostname: 'minioapi.realsoftgames.com',
				pathname: '/**'
			},
			{
				protocol: 'http',
				hostname: '************',
				port: '9000',
				pathname: '/**'
			}
		],
		// Allow unoptimized images for direct MinIO URLs
		unoptimized: true,
		// Increase image cache duration
		minimumCacheTTL: 604800, // 7 days in seconds
		// Disable blur-up effect for faster loading
		disableStaticImages: false,
		// Optimize formats
		formats: ['image/webp']
	},
	experimental: {
		serverActions: {
			allowedOrigins: ['localhost:3000', 'realsoftgames.com']
		}
	},
	output: 'standalone',
	webpack: (config, { dev, isServer }) => {
		// Add this for more detailed error messages in development
		if (dev && !isServer) {
			config.devtool = false; // Changed from 'inline-source-map' to avoid performance warnings
		}
		config.ignoreWarnings = [
			{ module: /node_modules\/mongoose/ },
			{ module: /node_modules\/mongodb/ }
		];
		return config;
	},
	async rewrites() {
		return [
			{
				source: '/uploads/:path*',
				destination: '/api/upload/:path*'
			},
			{
				source: '/image-proxy',
				destination: '/api/image-proxy'
			},
			{
				source: '/minio/:path*',
				destination: '/api/minio/:path*'
			},
			{
				source: '/optimized-image',
				destination: '/api/optimized-image'
			}
		];
	}
};

module.exports = nextConfig;
