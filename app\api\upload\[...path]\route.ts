import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import path from 'path';
import fs from 'fs/promises';

export async function GET(
	request: Request,
	{ params }: { params: Promise<{ path: string[] }> }
) {
	const { path: pathParts } = await params;
	const filePath = path.join(
		process.cwd(),
		'public',
		'uploads',
		...pathParts
	);

	try {
		const fileBuffer = await fs.readFile(filePath);
		const fileStats = await fs.stat(filePath);

		const response = new NextResponse(fileBuffer);

		response.headers.set('Content-Type', getContentType(filePath));
		response.headers.set('Content-Length', fileStats.size.toString());
		response.headers.set(
			'Cache-Control',
			'public, max-age=31536000, immutable'
		);

		return response;
	} catch (error) {
		console.error('Error serving file:', error);
		return new NextResponse('File not found', { status: 404 });
	}
}

function getContentType(filePath: string): string {
	const ext = path.extname(filePath).toLowerCase();
	switch (ext) {
		case '.jpg':
		case '.jpeg':
			return 'image/jpeg';
		case '.png':
			return 'image/png';
		case '.gif':
			return 'image/gif';
		case '.svg':
			return 'image/svg+xml';
		default:
			return 'application/octet-stream';
	}
}
