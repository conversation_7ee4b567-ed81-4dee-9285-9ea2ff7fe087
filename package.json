{"name": "realsoft-games", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "fix-nextjs": "powershell -ExecutionPolicy Bypass -File ./fix-nextjs.ps1"}, "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@aws-sdk/s3-request-presigner": "^3.787.0", "@hookform/resolvers": "^3.1.0", "@iconify/react": "^5.0.2", "@nextui-org/react": "^2.4.8", "@radix-ui/react-avatar": "^1.0.3", "@radix-ui/react-dropdown-menu": "^2.0.5", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.1.2", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-visually-hidden": "^1.2.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "20.4.5", "@types/react": "18.2.17", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "18.2.7", "adm-zip": "^0.5.16", "autoprefixer": "10.4.14", "axios": "^1.7.7", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.6.1", "clsx": "^1.2.1", "cmdk": "^1.0.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.0.0-rc11", "eslint": "8.46.0", "eslint-config-next": "13.4.12", "framer-motion": "^11.11.1", "immutability-helper": "^3.1.1", "input-otp": "^1.2.4", "isomorphic-dompurify": "^2.16.0", "lucide-react": "^0.263.1", "mime-types": "^3.0.1", "minio": "^8.0.5", "mongodb": "^5.9.2", "mongoose": "^8.7.0", "next": "^15.3.1", "next-auth": "^4.24.8", "next-themes": "^0.2.1", "node-fetch": "^3.3.2", "nodemailer": "^6.9.4", "postcss": "8.4.27", "radix-ui": "^1.0.1", "react": "18.2.0", "react-accordion": "^0.3.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^9.1.3", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "18.2.0", "react-error-boundary": "^4.0.10", "react-hook-form": "^7.45.2", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-quill": "^2.0.0", "react-resizable-panels": "^2.1.4", "react-select-country-list": "^2.2.3", "recharts": "^2.12.7", "rimraf": "^6.0.1", "sharp": "^0.33.5", "sonner": "^1.5.0", "tailwind-merge": "^1.14.0", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.6", "typescript": "5.1.6", "vaul": "^1.0.0", "zod": "^3.21.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/nodemailer": "^6.4.9", "@types/react-icons": "^3.0.0", "@types/react-select-country-list": "^2.2.3", "@types/youtube": "^0.1.0"}}