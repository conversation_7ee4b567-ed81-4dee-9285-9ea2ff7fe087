// Test script to verify API token usage counter
const fetch = require('node-fetch');

// Replace with your actual API token
const API_TOKEN = 'YOUR_API_TOKEN';
const BASE_URL = 'http://localhost:3000';

async function testApiToken() {
  try {
    console.log('Testing API token...');
    
    // Test the API token endpoint
    const response = await fetch(`${BASE_URL}/api/test-token`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`
      }
    });
    
    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}`);
    }
    
    const data = await response.json();
    console.log('API response:', data);
    console.log('API token test successful!');
    
    console.log('\nNow check the API token usage count in the dashboard.');
  } catch (error) {
    console.error('Error testing API token:', error);
  }
}

testApiToken();
