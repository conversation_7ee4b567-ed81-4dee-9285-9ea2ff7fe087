'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface MinioImageProps {
  src: string | null | undefined;
  alt?: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  sizes?: string;
  style?: React.CSSProperties;
  fill?: boolean;
  onLoadingComplete?: () => void;
}

/**
 * MinioImage - A specialized component for loading MinIO images with optimized performance
 * This component bypasses the proxy routes when possible for faster loading
 */
export default function MinioImage({
  src,
  alt,
  className,
  ...props
}: MinioImageProps) {
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(false);
  const [useDirectAccess, setUseDirectAccess] = useState(true);

  // Process the source URL on the client side only
  useEffect(() => {
    if (!src) {
      setIsLoading(false);
      setError(true);
      return;
    }

    // Try to detect if we're on the local network
    const isLocalNetwork = typeof window !== 'undefined' && (
      window.location.hostname === 'localhost' ||
      window.location.hostname === '************' ||
      window.location.hostname.startsWith('192.168.')
    );

    // Use direct URLs whenever possible for better performance
    if (typeof src === 'string') {
      // For MinIO URLs, use them directly without proxying
      if (src.includes('minioapi.realsoftgames.com')) {
        // Use the direct URL without any cache busting for better caching
        setImageSrc(src);
      } else if (src.includes('************')) {
        // For local network URLs, keep using local URLs for faster access when on local network
        setImageSrc(src);
      } else if (src.startsWith('/minio/')) {
        // For /minio/ paths, convert to direct MinIO URLs
        const pathParts = src.split('/');
        if (pathParts.length >= 3) {
          const bucket = pathParts[2];
          const key = pathParts.slice(3).join('/');

          if (isLocalNetwork) {
            // Use local MinIO URL for faster access when on local network
            setImageSrc(`http://************:9000/${bucket}/${key}`);
          } else {
            // Use public URL when accessing from outside
            setImageSrc(`https://minioapi.realsoftgames.com/${bucket}/${key}`);
          }
        } else {
          setImageSrc(src);
        }
      } else {
        // For non-MinIO images, use the original URL
        setImageSrc(src);
      }
    }
  }, [src]);

  const handleError = () => {
    // If direct access fails and we haven't tried the fallback yet
    if (!error && imageSrc && useDirectAccess) {
      console.log('Direct MinIO access failed, trying fallback:', imageSrc);
      setUseDirectAccess(false);

      // If we're using a direct MinIO URL, try the optimized image API
      if (imageSrc.includes('minioapi.realsoftgames.com') || imageSrc.includes('************')) {
        try {
          // Extract bucket and key from the URL
          const urlObj = new URL(imageSrc);
          const pathParts = urlObj.pathname.split('/');

          // Skip the first empty element if present
          const startIndex = pathParts[0] === '' ? 1 : 0;

          if (pathParts.length >= startIndex + 2) {
            const bucket = pathParts[startIndex];
            const key = pathParts.slice(startIndex + 1).join('/');

            // Use the optimized image API as a fallback, but with minimal processing
            const optimizedUrl = `/api/optimized-image?bucket=${encodeURIComponent(bucket)}&key=${encodeURIComponent(key)}&noprocess=true`;
            console.log('Using fallback URL:', optimizedUrl);
            setImageSrc(optimizedUrl);
            return; // Don't set error yet, give the optimized API a chance
          }
        } catch (err) {
          console.error('Error parsing MinIO URL:', err);
        }
      }

      // Mark as error after trying fallback
      setError(true);
    } else if (!useDirectAccess) {
      // If fallback also failed, show error state
      console.error('Both direct access and fallback failed for image:', imageSrc);
      setError(true);
    }

    // If all fallbacks fail, show error state
    setIsLoading(false);
  };

  // If no valid source is available, render a placeholder
  if (!imageSrc) {
    return (
      <div className={`bg-gray-200 dark:bg-gray-700 rounded ${className || ''}`}
           style={props.fill ? { width: '100%', height: '100%', ...props.style } : { width: props.width || 500, height: props.height || 300, ...props.style }}>
        <div className="flex items-center justify-center h-full w-full">
          <span className="text-gray-400 dark:text-gray-500">No Image</span>
        </div>
      </div>
    );
  }

  // Determine appropriate sizes for responsive images
  const getSizes = () => {
    if (props.fill) {
      return '100vw'; // Full width for fill mode
    } else if (props.sizes) {
      return props.sizes;
    } else {
      // Default responsive sizes
      return '(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw';
    }
  };

  // Add width parameter for responsive images
  const getOptimizedSrc = (src: string, width?: number) => {
    if (!src.includes('/api/optimized-image')) return src;

    const url = new URL(src, window.location.origin);
    if (width) {
      url.searchParams.set('w', width.toString());
    }
    return url.toString();
  };

  return (
    <div className={`relative ${className || ''}`} style={props.fill ? { width: '100%', height: '100%', ...props.style } : props.style}>
      <Image
        {...props}
        className={`${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300 ${props.className || ''}`}
        src={imageSrc}
        alt={alt || 'Image'}
        onError={handleError}
        onLoad={() => {
          setIsLoading(false);
          if (props.onLoadingComplete) {
            props.onLoadingComplete();
          }
        }}
        unoptimized={useDirectAccess && (imageSrc.includes('minioapi.realsoftgames.com') || imageSrc.includes('************'))} // Skip Next.js optimization for direct MinIO URLs
        width={props.fill ? undefined : (props.width || 500)}
        height={props.fill ? undefined : (props.height || 300)}
        sizes={getSizes()}
        quality={props.quality || 80}
        placeholder="blur"
        blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEDQIHXG8ZQQAAAABJRU5ErkJggg=="
      />
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200 dark:bg-gray-700 animate-pulse rounded">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
}
