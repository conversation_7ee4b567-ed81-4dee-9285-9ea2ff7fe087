'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { siteConfig } from '@/lib/siteConfig';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ExternalLink } from 'lucide-react';
import { FaDiscord } from 'react-icons/fa';

export default function DiscordBanner() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, 300);
    return () => clearTimeout(timer);
  }, []);

  return (
    <Card className={`overflow-hidden transition-all duration-700 transform ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}>
      <CardContent className="p-0">
        <div className="bg-[#5865F2] dark:bg-[#5865F2]/90 p-6 flex flex-col md:flex-row items-center gap-6">
          <div className="flex-shrink-0 bg-white/10 p-4 rounded-full">
            <FaDiscord className="h-10 w-10 text-white" />
          </div>

          <div className="flex-grow text-center md:text-left">
            <h2 className="text-white text-xl font-bold mb-2">Get Faster Responses on Discord</h2>
            <p className="text-white/90 mb-4">
              Join our Discord server for real-time support and community discussions.
              Responses are generally faster than email!
            </p>

            <div className="flex flex-wrap gap-4 justify-center md:justify-start">
              <Button
                asChild
                variant="secondary"
                size="lg"
                className="bg-white text-[#5865F2] hover:bg-white/90 group transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
              >
                <Link href={siteConfig.social.discord} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2">
                  Join Discord
                  <ExternalLink className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
                </Link>
              </Button>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-[#5865F2]/10 to-transparent dark:from-[#5865F2]/5 dark:to-transparent p-4 text-sm text-muted-foreground">
          <p className="flex items-start gap-2">
            <span className="text-[#5865F2] font-bold">Tip:</span>
            <span>For technical support, bug reports, or feature requests, Discord is our recommended channel for the fastest assistance.</span>
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
