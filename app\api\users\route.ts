import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongoose';
import User from '@/models/User';
import { UserRole } from '@/types/global';
import { convertToDirectUrl } from '@/lib/minio';
import { cookies, headers } from 'next/headers';

let isConnected = false;

export async function GET(req: Request) {
	try {
		// Get the session directly without using cookies/headers in this route
		const session = await getServerSession(await authOptions());

		if (!session || session.user.role !== UserRole.ADMIN) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
		}
		if (!isConnected) {
			await connectToDatabase();
			isConnected = true;
		}

		const { searchParams } = new URL(req.url);
		const page = parseInt(searchParams.get('page') || '1', 10);
		const limit = parseInt(searchParams.get('limit') || '100', 10);
		const skip = (page - 1) * limit;

		const users = await User.find({}, '-password')
			.skip(skip)
			.limit(limit)
			.lean();

		const total = await User.countDocuments();

		// Format the user data to convert MongoDB ObjectIds to strings and fix avatar URLs
		const formattedUsers = users.map(user => {
			// Ensure user is properly typed
			const typedUser = user as any;
			return {
				...typedUser,
				id: typedUser._id.toString(),
				_id: typedUser._id.toString(),
				// Convert avatar URL to direct MinIO URL if it exists
				avatar: typedUser.avatar ? convertToDirectUrl(typedUser.avatar) : undefined
			};
		});

		return NextResponse.json({
			users: formattedUsers,
			page,
			totalPages: Math.ceil(total / limit),
			total
		});
	} catch (error) {
		console.error('Error fetching users:', error);
		return NextResponse.json(
			{ error: 'Internal Server Error' },
			{ status: 500 }
		);
	}
}
