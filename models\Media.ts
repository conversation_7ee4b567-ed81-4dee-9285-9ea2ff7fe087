import mongoose from 'mongoose';

export interface IMedia {
	_id?: string;
	type: 'image' | 'youtube' | 'webgl';
	url: string;
	width?: number; // For WebGL canvas width
	height?: number; // For WebGL canvas height
	title?: string; // For WebGL build title
	indexUrl?: string; // For WebGL index.html file
	loaderUrl?: string; // For WebGL loader file
	dataUrl?: string; // For WebGL data file
	frameworkUrl?: string; // For WebGL framework file
	codeUrl?: string; // For WebGL code file (legacy)
	wasmUrl?: string; // For WebGL wasm file (modern)
	memoryUrl?: string; // For WebGL memory file (optional)
	backgroundUrl?: string; // For WebGL background image (optional)
	companyName?: string; // For WebGL company name
	productName?: string; // For WebGL product name
	buildVersion?: string; // For WebGL build version
	demoUrl?: string; // For custom demo URL
}

export interface IMediaFrontend {
	_id?: string;
	type: 'image' | 'youtube' | 'webgl';
	url: string;
	status?: 'uploading' | 'error' | 'success';
	progress?: number;
	isTemp?: boolean;
	width?: number; // For WebGL canvas width
	height?: number; // For WebGL canvas height
	title?: string; // For WebGL build title
	indexUrl?: string; // For WebGL index.html file
	loaderUrl?: string; // For WebGL loader file
	dataUrl?: string; // For WebGL data file
	frameworkUrl?: string; // For WebGL framework file
	codeUrl?: string; // For WebGL code file (legacy)
	wasmUrl?: string; // For WebGL wasm file (modern)
	memoryUrl?: string; // For WebGL memory file (optional)
	backgroundUrl?: string; // For WebGL background image (optional)
	companyName?: string; // For WebGL company name
	productName?: string; // For WebGL product name
	buildVersion?: string; // For WebGL build version
	demoUrl?: string; // For custom demo URL
}

const MediaSchema = new mongoose.Schema({
	type: { type: String, enum: ['image', 'youtube', 'webgl'], required: true },
	url: { type: String, required: true },
	width: { type: Number }, // For WebGL canvas width
	height: { type: Number }, // For WebGL canvas height
	title: { type: String }, // For WebGL build title
	indexUrl: { type: String }, // For WebGL index.html file
	loaderUrl: { type: String }, // For WebGL loader file
	dataUrl: { type: String }, // For WebGL data file
	frameworkUrl: { type: String }, // For WebGL framework file
	codeUrl: { type: String }, // For WebGL code file (legacy)
	wasmUrl: { type: String }, // For WebGL wasm file (modern)
	memoryUrl: { type: String }, // For WebGL memory file (optional)
	backgroundUrl: { type: String }, // For WebGL background image (optional)
	companyName: { type: String }, // For WebGL company name
	productName: { type: String }, // For WebGL product name
	buildVersion: { type: String }, // For WebGL build version
	demoUrl: { type: String } // For custom demo URL
});

export function mediaToFrontend(media: IMedia): IMediaFrontend {
	return {
		_id: media._id?.toString(),
		type: media.type,
		url: media.url,
		status: 'success',
		isTemp: false,
		width: media.width,
		height: media.height,
		title: media.title,
		indexUrl: media.indexUrl,
		loaderUrl: media.loaderUrl,
		dataUrl: media.dataUrl,
		frameworkUrl: media.frameworkUrl,
		codeUrl: media.codeUrl,
		wasmUrl: media.wasmUrl,
		memoryUrl: media.memoryUrl,
		backgroundUrl: media.backgroundUrl,
		companyName: media.companyName,
		productName: media.productName,
		buildVersion: media.buildVersion,
		demoUrl: media.demoUrl
	};
}

export function frontendToMedia(media: IMediaFrontend): IMedia {
	return {
		_id: media._id,
		type: media.type,
		url: media.url,
		width: media.width,
		height: media.height,
		title: media.title,
		indexUrl: media.indexUrl,
		loaderUrl: media.loaderUrl,
		dataUrl: media.dataUrl,
		frameworkUrl: media.frameworkUrl,
		codeUrl: media.codeUrl,
		wasmUrl: media.wasmUrl,
		memoryUrl: media.memoryUrl,
		backgroundUrl: media.backgroundUrl,
		companyName: media.companyName,
		productName: media.productName,
		buildVersion: media.buildVersion,
		demoUrl: media.demoUrl
	};
}

export default MediaSchema;
