import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import { compare } from 'bcryptjs';
import { connectToDatabase } from './mongoose';
import UserModel, { IUser } from '@/models/User';
import { UserRole } from '@/types/global';
import { convertToDirectUrl } from './minio';

// Define a type that matches the structure we're returning
type AuthUser = {
	id: string;
	username: string;
	name: string;
	email: string;
	role: UserRole;
	dateOfBirth: string;
	country?: string;
	avatar?: string;
};

export const authOptions = async (): Promise<NextAuthOptions> => {
	// Import cookies and headers dynamically to avoid issues
	try {
		if (typeof window === 'undefined') {
			const { cookies, headers } = await import('next/headers');
			// Access cookies and headers to ensure they're properly initialized
			const cookiesList = await cookies();
			const headersList = await headers();
		}
	} catch (error) {
		console.error('Error initializing cookies and headers:', error);
		// Continue even if there's an error - NextAuth will handle it
	}
	return {
		providers: [
			CredentialsProvider({
				name: 'Credentials',
				credentials: {
					email: { label: 'Email', type: 'text' },
					password: { label: 'Password', type: 'password' }
				},
				async authorize(credentials): Promise<AuthUser | null> {
					if (!credentials?.email || !credentials?.password) {
						return null;
					}

				try {
					await connectToDatabase();
					const user = await UserModel.findOne({ email: credentials.email });

					if (!user) {
						return null;
					}

					const isPasswordValid = await compare(
						credentials.password,
						user.password
					);

					if (!isPasswordValid) {
						return null;
					}
					return {
						id: user._id.toString(),
						username: user.username,
						name: user.name,
						email: user.email,
						role: user.role as UserRole,
						dateOfBirth: user.dateOfBirth,
						country: user.country || undefined,
						avatar: user.avatar ? convertToDirectUrl(user.avatar) : undefined
					};
				} catch (error) {
					return null;
				}
			}
		})
	],
	callbacks: {
		async jwt({ token, user }) {
			if (user) {
				token.id = user.id;
				token.username = user.username;
				token.name = user.name;
				token.email = user.email;
				token.role = user.role;
				token.dateOfBirth = user.dateOfBirth;
				token.country = user.country;
				token.avatar = user.avatar;
			}
			return token;
		},
		async session({ session, token }) {
			if (session.user) {
				session.user.id = token.id as string;
				session.user.username = token.username as string;
				session.user.name = token.name;
				session.user.email = token.email as string;
				session.user.role = token.role as UserRole;
				session.user.dateOfBirth = token.dateOfBirth as string;
				// Ensure country is properly set in the session
				console.log('Auth: Token country value:', token.country);
				session.user.country = token.country as string | undefined;
				console.log('Auth: Setting country in session:', session.user.country);
				// Ensure avatar URL is in the direct MinIO format
				const avatarUrl = token.avatar as string | undefined;
				session.user.avatar = avatarUrl ? convertToDirectUrl(avatarUrl) : undefined;
				// Also update the token's avatar URL
				token.avatar = session.user.avatar;
			}
			return session;
		}
	},
	pages: {
		signIn: '/login'
	},
	session: {
		strategy: 'jwt'
	},
	secret: process.env.NEXTAUTH_SECRET
	};
};


