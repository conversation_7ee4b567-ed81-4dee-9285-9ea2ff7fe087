import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongoose';
import Project from '@/models/Project';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import path from 'path';
import fs from 'fs';
import { serializeData } from '@/lib/serialization';
import { cookies, headers } from 'next/headers';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Explicitly await cookies and headers to avoid warnings
    const cookiesList = await cookies();
    const headersList = await headers();

    await connectToDatabase();

    const { id } = await params;
    const project = await Project.findById(id).lean();

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Use our serialization utility to handle all MongoDB objects
    const serializedProject = serializeData(project);

    return NextResponse.json(serializedProject);
  } catch (error) {
    console.error('Error fetching project:', error);
    return NextResponse.json(
      { error: 'Failed to fetch project' },
      { status: 500 }
    );
  }
}

export async function PATCH(
	req: Request,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		await connectToDatabase();

		// Explicitly await cookies and headers to avoid warnings
		const cookiesList = await cookies();
		const headersList = await headers();

		// Await authOptions to properly handle cookies and headers
		const options = await authOptions();
		const session = await getServerSession(options);
		if (!session || !session.user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { id } = await params;
		const data = await req.json();

		const project = await Project.findByIdAndUpdate(
			id,
			{
				...data,
				liveDemoLink: data.liveDemoLink
			},
			{ new: true }
		);

		if (!project) {
			return NextResponse.json({ error: 'Project not found' }, { status: 404 });
		}

		// Serialize the project to avoid MongoDB object issues
		const serializedProject = serializeData(project.toObject());
		return NextResponse.json(serializedProject);
	} catch (error) {
		console.error('Error updating project:', error);
		return NextResponse.json(
			{
				error: 'Failed to update project',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
}

export async function DELETE(
	req: Request,
	{ params }: { params: Promise<{ id: string }> }
) {
	try {
		await connectToDatabase();

		// Explicitly await cookies and headers to avoid warnings
		const cookiesList = await cookies();
		const headersList = await headers();

		// Await authOptions to properly handle cookies and headers
		const options = await authOptions();
		const session = await getServerSession(options);
		if (!session || !session.user) {
			return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
		}

		const { id } = await params;

		const project = await Project.findById(id);

		if (!project) {
			return NextResponse.json({ error: 'Project not found' }, { status: 404 });
		}

		// Delete associated media files
		for (const mediaItem of project.media) {
			if (mediaItem.type === 'image' && mediaItem.url.startsWith('/uploads/')) {
				const filePath = path.join(process.cwd(), 'public', mediaItem.url);
				if (fs.existsSync(filePath)) {
					fs.unlinkSync(filePath);
				}
			}
		}

		// Delete the project from the database
		await Project.findByIdAndDelete(id);

		return NextResponse.json({
			message: 'Project and associated media deleted successfully'
		});
	} catch (error) {
		console.error('Error deleting project:', error);
		return NextResponse.json(
			{
				error: 'Failed to delete project',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
}
