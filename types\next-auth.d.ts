import { UserRole } from '@/types/global';
import { User } from '@/models/User';

declare module 'next-auth' {
	interface Session {
		user: User;
	}

	interface User {
		id: string;
		username: string;
		email: string;
		role: UserRole;
		dateOfBirth: string; // Changed from Date to string
		country?: string;
		avatar?: string;
	}
}

declare module 'next-auth/jwt' {
	interface JWT extends Omit<User, 'id'> {
		id: string;
	}
}
