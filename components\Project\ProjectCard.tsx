'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import MinioImage from '@/components/MinioImage';
import {
	<PERSON>,
	CardContent,
	CardFooter,
	CardHeader,
	CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { IProject } from '@/models/Project';
import { ArrowRight } from 'lucide-react';
import 'react-quill/dist/quill.snow.css';

interface ProjectCardProps {
	project: IProject;
}

const truncateDescription = (description: string, maxWords: number = 20) => {
	const strippedText = description.replace(/<[^>]+>/g, '');
	const words = strippedText.split(/\s+/);
	if (words.length <= maxWords) return description;
	const truncated = words.slice(0, maxWords).join(' ') + '...';
	return `<div class="ql-editor" style="padding: 0;">${truncated}</div>`;
};

export function ProjectCard({ project }: ProjectCardProps) {
	const [isMounted, setIsMounted] = useState(false);
	const [isHovered, setIsHovered] = useState(false);
	const [truncatedDescription, setTruncatedDescription] = useState<string>('');

	useEffect(() => {
		setIsMounted(true);
		setTruncatedDescription(truncateDescription(project.description));
	}, [project.description]);

	return (
		<Card
			className="flex flex-col w-full max-w-sm relative transition-all duration-300 hover:shadow-lg hover:-translate-y-1 overflow-hidden"
			onMouseEnter={() => setIsHovered(true)}
			onMouseLeave={() => setIsHovered(false)}
		>
			{project.isFeatured && (
				<div className="absolute top-0 right-0 bg-yellow-400 text-yellow-900 text-xs font-bold px-2 py-1 rounded-bl-md z-10">
					Featured
				</div>
			)}
			<CardHeader className={project.isFeatured ? 'pt-8' : ''}>
				<CardTitle className="transition-colors duration-300 group-hover:text-primary">{project.title}</CardTitle>
			</CardHeader>
			<CardContent className="flex-grow">
				{project.media && project.media.length > 0 && (
					<div className="relative w-full h-48 mb-4 overflow-hidden rounded-md">
						{project.media[0].type === 'image' ? (
							<>
								<MinioImage
									src={project.media[0].url}
									alt={project.title}
									className={`rounded-md w-full h-full object-cover transition-transform duration-500 ${isHovered ? 'scale-105' : 'scale-100'}`}
									style={{ objectFit: 'cover' }}
									priority={project.isFeatured}
								/>
								<div className={`absolute inset-0 bg-black transition-opacity duration-300 ${isHovered ? 'opacity-10' : 'opacity-0'}`}></div>
							</>
						) : (
							<iframe
								src={project.media[0].url}
								title={project.title}
								className="w-full h-full rounded-md"
								allowFullScreen
							/>
						)}
					</div>
				)}
				<div className="mt-2 mb-3 text-sm text-muted-foreground">
					{isMounted ? (
						<div
							className="prose prose-sm max-w-none dark:prose-invert"
							dangerouslySetInnerHTML={{
								__html: truncatedDescription
							}}
						/>
					) : (
						<div className="h-16 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"></div>
					)}
				</div>
				<div className="font-bold text-lg mt-2 transition-colors duration-300">
					{Number(project.price) === 0 ? 'Free' : `$${project.price}`}
				</div>
			</CardContent>
			<CardFooter>
				<Button
					asChild
					className="w-full group transition-all duration-300 hover:bg-primary/90"
				>
					<Link href={`/projects/${project._id}`} className="flex items-center justify-center gap-2">
						View Details
						<ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
					</Link>
				</Button>
			</CardFooter>
		</Card>
	);
}
