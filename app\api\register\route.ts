import { NextResponse } from 'next/server';
import { hash } from 'bcryptjs';
import { connectToDatabase } from '@/lib/mongoose';
import User from '@/models/User';
import { UserRole } from '@/types/global';

export async function POST(request: Request) {
	try {
		await connectToDatabase();

		const { username, name, email, password, dateOfBirth } =
			await request.json();

		// Check if user already exists
		const existingUser = await User.findOne({ $or: [{ email }, { username }] });
		if (existingUser) {
			if (existingUser.email === email) {
				return NextResponse.json(
					{ message: 'A user with this email already exists' },
					{ status: 400 }
				);
			}
			if (existingUser.username === username) {
				return NextResponse.json(
					{ message: 'This username is already taken' },
					{ status: 400 }
				);
			}
		}

		// Hash password
		const hashedPassword = await hash(password, 10);

		// Create new user
		const newUser = new User({
			username,
			name,
			email,
			password: hashedPassword,
			dateOfBirth,
			role: UserRole.USER
		});

		await newUser.save();

		return NextResponse.json(
			{ message: 'User registered successfully' },
			{ status: 201 }
		);
	} catch (error) {
		console.error('Registration error:', error);
		return NextResponse.json(
			{ message: 'An error occurred while registering the user' },
			{ status: 500 }
		);
	}
}
