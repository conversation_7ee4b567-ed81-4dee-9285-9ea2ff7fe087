'use client';

import { useToast } from '@/hooks/use-toast';
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
  type ToastProps,
  type ToastActionElement,
} from '@/components/ui/toast';

export function Toaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider>
      {toasts.map(({ id, title, description, action, variant, type, ...props }) => {
        // Map legacy type to variant if needed
        const toastVariant = variant ||
          (type === 'success' ? 'success' :
           type === 'error' ? 'destructive' :
           type === 'warning' ? 'warning' :
           type === 'info' ? 'info' : 'default');

        // Remove type from props to avoid passing it to Toast
        const cleanProps = { ...props };
        delete (cleanProps as any).type;

        return (
          <Toast key={id} variant={toastVariant} {...cleanProps}>
            <div className="grid gap-1">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription>{description}</ToastDescription>
              )}
            </div>
            {action}
            <ToastClose />
          </Toast>
        );
      })}
      <ToastViewport />
    </ToastProvider>
  );
}
