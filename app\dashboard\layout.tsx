'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { UserRole } from '@/types/global';
import { cn } from '@/lib/utils';
import ErrorBoundary from '@/components/error-boundary';
import { Menu, LayoutDashboard, User, Users, FolderOpen, ShoppingBag, Tag, Image, Layers, Gift, Key } from 'lucide-react';

export default function DashboardLayout({
	children
}: {
	children: React.ReactNode;
}) {
	const { data: session, status } = useSession();
	const pathname = usePathname();
	const [isSidebarOpen, setIsSidebarOpen] = useState(false);

	if (status === 'loading') {
		return (
			<div className="flex items-center justify-center h-screen">
				Loading...
			</div>
		);
	}

	if (!session) {
		return (
			<div className="flex items-center justify-center h-screen">
				Access Denied
			</div>
		);
	}

	const isAdmin = session.user?.role === UserRole.ADMIN;

	const navItems = [
		{ href: '/dashboard', label: 'Dashboard', icon: LayoutDashboard, showAlways: true },
		{ href: '/dashboard/profile', label: 'Profile', icon: User, showAlways: true },
		{ href: '/dashboard/users', label: 'Users', icon: Users, adminOnly: true },
		{ href: '/dashboard/products', label: 'Products', icon: ShoppingBag, adminOnly: true },
		{ href: '/dashboard/projects', label: 'Projects', icon: FolderOpen, adminOnly: true },
		{ href: '/dashboard/api-tokens', label: 'API Tokens', icon: Key, adminOnly: true }
	];

	return (
		<div className="flex flex-col md:flex-row min-h-screen bg-background">
			<aside
				className={`bg-card text-card-foreground w-full md:w-64 ${
					isSidebarOpen ? 'block' : 'hidden'
				} md:block border-r border-border`}
			>
				<div className="p-4 border-b border-border">
					<h2 className="text-xl font-bold">{isAdmin ? 'Admin Dashboard' : 'Dashboard'}</h2>
				</div>
				<nav className="p-4">
					{navItems.map((item) => {
						if (item.adminOnly && !isAdmin) return null;
						if (!item.showAlways && !isAdmin) return null;
						const Icon = item.icon;
						return (
							<Link key={item.href} href={item.href} passHref>
								<Button
									variant={pathname === item.href ? 'secondary' : 'ghost'}
									className={cn(
										'w-full justify-start mb-1',
										pathname === item.href && 'bg-accent'
									)}
									onClick={() => setIsSidebarOpen(false)}
								>
									<Icon className="mr-2 h-4 w-4" />
									{item.label}
								</Button>
							</Link>
						);
					})}
				</nav>
			</aside>
			<main className="flex-1 p-4 md:p-8 bg-background">
				<div className="flex justify-between items-center mb-6">
					<Button
						variant="ghost"
						className="md:hidden"
						onClick={() => setIsSidebarOpen(!isSidebarOpen)}
					>
						<Menu className="h-6 w-6" />
					</Button>
					<div className="md:hidden">
						<h1 className="text-xl font-bold">{isAdmin ? 'Admin Dashboard' : 'Dashboard'}</h1>
					</div>
					<div></div> {/* Empty div for flex spacing */}
				</div>
				<ErrorBoundary componentName="Dashboard">{children}</ErrorBoundary>
			</main>
		</div>
	);
}
