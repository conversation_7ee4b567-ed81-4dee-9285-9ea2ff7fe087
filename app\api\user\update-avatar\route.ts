import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongoose';
import mongoose from 'mongoose';
import User from '@/models/User';
import { deleteFile, convertToDirectUrl } from '@/lib/minio';

export async function POST(req: Request) {
	try {
		// Explicitly await cookies to avoid the sync dynamic APIs warning
		const options = await authOptions();
		const session = await getServerSession(options);
		console.log('Update Avatar API: Session:', session?.user?.id);

		if (!session) {
			console.log('Update Avatar API: Not authenticated');
			return NextResponse.json({ error: 'Not authenticated' }, { status: 401 });
		}

		const { avatarUrl } = await req.json();
		console.log('Update Avatar API: Received avatar URL:', avatarUrl);

		await connectToDatabase();
		console.log('Update Avatar API: Connected to database');

		const user = await User.findById(
			new mongoose.Types.ObjectId(session.user.id)
		);
		console.log('Update Avatar API: Found user:', user?._id.toString());

		// Try to delete the old avatar from MinIO if it exists
		if (user && user.avatar) {
			try {
				// Convert to direct URL if needed
				const directUrl = convertToDirectUrl(user.avatar);
				console.log('Update Avatar API: Deleting old avatar:', directUrl);
				await deleteFile(directUrl);
				console.log('Update Avatar API: Old avatar deleted successfully');
			} catch (error) {
				console.log('Update Avatar API: Failed to delete old avatar:', error);
				// Continue with the update even if deletion fails
			}
		}

		console.log('Update Avatar API: Updating user with new avatar URL:', avatarUrl);
		const updatedUser = await User.findOneAndUpdate(
			{ _id: new mongoose.Types.ObjectId(session.user.id) },
			{ $set: { avatar: avatarUrl } },
			{ new: true }
		);

		if (updatedUser) {
			// Generate a timestamp for cache busting
			const timestamp = Date.now();
			console.log('Update Avatar API: User updated successfully with timestamp:', timestamp);

			// Create a direct URL for the avatar
			const directUrl = convertToDirectUrl(updatedUser.avatar);
			console.log('Update Avatar API: Direct URL for avatar:', directUrl);

			const response = {
				message: 'Avatar updated successfully',
				avatarUrl: updatedUser.avatar,
				directUrl: directUrl,
				timestamp: timestamp
			};
			console.log('Update Avatar API: Sending response:', response);

			return NextResponse.json(
				response,
				{
					status: 200,
					headers: {
						'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate'
					}
				}
			);
		} else {
			console.log('Update Avatar API: Failed to update user');
			return NextResponse.json(
				{ error: 'Failed to update avatar' },
				{ status: 400 }
			);
		}
	} catch (error) {
		console.error('Update Avatar API: Internal server error:', error);
		return NextResponse.json(
			{ error: 'Internal server error' },
			{ status: 500 }
		);
	}
}
