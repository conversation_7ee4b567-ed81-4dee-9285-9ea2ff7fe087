import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongoose';
import Product from '@/models/Product';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { serializeData } from '@/lib/serialization';
import { withApiAuth } from '@/app/api/middleware';
import { withCors } from '@/app/api/cors-middleware';
import { cookies, headers } from 'next/headers';

async function handleGET(req: Request, apiToken: any) {
	try {
		await connectToDatabase();

		const { searchParams } = new URL(req.url);
		const featured = searchParams.get('featured');

		let query = {};
		if (featured === 'true') {
			query = { isFeatured: true };
		}

		const products = await Product.find(query).sort({ order: 1 }).lean();

		// Serialize the products to avoid MongoDB object issues
		const serializedProducts = serializeData(products);

		return NextResponse.json(serializedProducts);
	} catch (error) {
		console.error('Error in GET /api/products:', error);
		return NextResponse.json(
			{ error: 'Internal Server Error' },
			{ status: 500 }
		);
	}
}

// Use the API token validation middleware with CORS
export const GET = async (req: NextRequest) => {
  // Always await cookies and headers at the top level
  const cookiesList = await cookies();
  const headersList = await headers();

  // Now pass to withCors and withApiAuth
  return withCors(req, (req) => withApiAuth(req, handleGET));
};

export const POST = async (req: NextRequest) => {
  // Always await cookies and headers at the top level
  const cookiesList = await cookies();
  const headersList = await headers();

  return withCors(req, async (req) => {
    try {
      await connectToDatabase();

      // We've already awaited cookies and headers at the top level

      const options = await authOptions();
      const session = await getServerSession(options);
      if (!session || !session.user) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      const data = await req.json();

      const product = new Product({
        ...data,
        userId: session.user.id
      });

      await product.save();

      // Serialize the product to avoid MongoDB object issues
      const serializedProduct = serializeData(product.toObject());

      return NextResponse.json(serializedProduct, { status: 201 });
    } catch (error) {
      console.error('Error in POST /api/products:', error);
      return NextResponse.json(
        { error: 'Failed to create product' },
        { status: 500 }
      );
    }
  });
};

// Handle OPTIONS requests for CORS
export const OPTIONS = async (req: NextRequest) => {
  // Always await cookies and headers at the top level
  const cookiesList = await cookies();
  const headersList = await headers();

  return withCors(req, async () => new NextResponse(null, { status: 204 }));
};
