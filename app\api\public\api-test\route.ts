import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { withApiAuth } from '@/app/api/middleware';
import { withCors } from '@/app/api/cors-middleware';

export const dynamic = 'force-dynamic';

/**
 * Test endpoint for API token authentication
 */
async function handler(req: Request, apiToken: any) {
  console.log('Public API test endpoint called with token:', apiToken ? apiToken._id : 'unknown');
  console.log('Token usage count:', apiToken ? apiToken.usageCount : 'unknown');

  return NextResponse.json({
    success: true,
    message: 'API token is valid',
    timestamp: new Date().toISOString(),
    tokenInfo: {
      id: apiToken ? apiToken._id.toString() : 'unknown',
      name: apiToken ? apiToken.name : 'unknown',
      usageCount: apiToken ? apiToken.usageCount : 'unknown',
      lastUsed: apiToken ? apiToken.lastUsed : 'unknown'
    }
  });
}

export const GET = (req: NextRequest) => withCors(req, (req) => withApiAuth(req, handler));

// Handle OPTIONS requests for CORS
export const OPTIONS = (req: NextRequest) => withCors(req, async () => new NextResponse(null, { status: 204 }));
