'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { convertToDirectUrl } from '@/lib/minio';
import Image from 'next/image';

export default function AvatarTestPage() {
  const { data: session, status } = useSession();
  const [directUrl, setDirectUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (status === 'authenticated' && session?.user?.avatar) {
      try {
        console.log('Original avatar URL:', session.user.avatar);
        const directUrl = convertToDirectUrl(session.user.avatar);
        console.log('Converted URL:', directUrl);

        // Create a proxy URL
        const proxyUrl = `/api/avatar-proxy?url=${encodeURIComponent(directUrl)}`;
        console.log('Proxy URL:', proxyUrl);

        setDirectUrl(proxyUrl);
      } catch (err) {
        console.error('Error converting URL:', err);
        setError('Failed to convert URL');
      } finally {
        setLoading(false);
      }
    } else if (status === 'authenticated') {
      setError('No avatar URL found in session');
      setLoading(false);
    } else if (status === 'unauthenticated') {
      setError('User not authenticated');
      setLoading(false);
    }
  }, [session, status]);

  if (loading) {
    return <div className="p-8">Loading...</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Avatar Test Page</h1>

      <div className="mb-4">
        <h2 className="text-xl font-semibold mb-2">Session Data</h2>
        <pre className="bg-gray-100 dark:bg-gray-800 p-4 rounded overflow-auto max-h-60">
          {JSON.stringify(session, null, 2)}
        </pre>
      </div>

      {error ? (
        <div className="text-red-500 mb-4">{error}</div>
      ) : directUrl ? (
        <div className="mb-4">
          <h2 className="text-xl font-semibold mb-2">Avatar URL</h2>
          <div className="mb-2">
            <strong>Direct URL:</strong> {directUrl}
          </div>

          <div className="mt-4">
            <h3 className="text-lg font-semibold mb-2">Avatar Image Test</h3>
            <div className="border border-gray-300 dark:border-gray-700 p-4 rounded">
              <div className="mb-4">
                <h4 className="font-medium mb-2">Using Next Image:</h4>
                <div className="relative h-20 w-20 rounded-full overflow-hidden">
                  <Image
                    src={directUrl}
                    alt="Avatar"
                    fill
                    className="object-cover"
                    onError={() => console.error('Next Image failed to load')}
                  />
                </div>
              </div>

              <div className="mb-4">
                <h4 className="font-medium mb-2">Using regular img tag:</h4>
                <img
                  src={directUrl}
                  alt="Avatar"
                  className="h-20 w-20 rounded-full object-cover"
                  onError={() => console.error('Regular img failed to load')}
                />
              </div>
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
}
