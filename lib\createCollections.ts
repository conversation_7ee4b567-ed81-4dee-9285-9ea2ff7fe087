import mongoose from 'mongoose'
import { connectToDatabase } from './mongoose'

export async function createCollections() {
  if (typeof window === 'undefined') {
    try {
      await connectToDatabase()

      const collections = ['users', 'projects', 'products', 'visitors']

      for (const collectionName of collections) {
        if (!mongoose.connection.collections[collectionName]) {
          await mongoose.connection.createCollection(collectionName)
          console.log(`Collection ${collectionName} created.`)
        }
      }
    } catch (error) {
      console.error('Error creating collections:', error)
    }
  }
}