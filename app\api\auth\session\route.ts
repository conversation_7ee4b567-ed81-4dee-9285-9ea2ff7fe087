import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from "@/lib/auth"
import { cookies, headers } from 'next/headers'

export const dynamic = 'force-dynamic'

// Use the standard NextAuth approach for session
export async function GET(req: Request) {
  try {
    // Explicitly await cookies and headers to avoid warnings
    const cookieStore = cookies();
    const headersList = headers();

    // Get auth options
    const options = await authOptions();

    // Get the session
    const session = await getServerSession(options);



    // Log the session for debugging
    if (session?.user) {
      console.log('Session API: User data:', {
        id: session.user.id,
        username: session.user.username,
        country: session.user.country,
        avatar: session.user.avatar ? 'exists' : 'missing'
      });
    } else {
      console.log('Session API: No session found');
    }

    // Return the session or null
    return NextResponse.json(session || { user: null });
  } catch (error) {
    console.error('Session API error:', error)
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 })
  }
}