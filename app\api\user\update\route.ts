import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { connectToDatabase } from '@/lib/mongoose';
import User from '@/models/User';
import bcrypt from 'bcryptjs';

export async function POST(req: Request) {
	try {
		// Await authOptions to properly handle cookies and headers
		const options = await authOptions();
		const session = await getServerSession(options);
		if (!session) {
			return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
		}

		await connectToDatabase();
		const updateData = await req.json();

		const user = await User.findOne({ email: session.user.email });

		if (!user) {
			return NextResponse.json({ message: 'User not found' }, { status: 404 });
		}

		if (
			updateData.email !== user.email ||
			updateData.username !== user.username
		) {
			if (!updateData.currentPassword) {
				return NextResponse.json(
					{
						message: 'Current password is required to update email or username'
					},
					{ status: 400 }
				);
			}

			const isPasswordValid = await bcrypt.compare(
				updateData.currentPassword,
				user.password
			);
			if (!isPasswordValid) {
				return NextResponse.json(
					{ message: 'Current password is incorrect' },
					{ status: 400 }
				);
			}
		}

		// Remove currentPassword from updateData
		delete updateData.currentPassword;

		// Log the update data for debugging
		console.log('User update API: Update data:', updateData);

		// Update user
		const updatedUser = await User.findOneAndUpdate(
			{ email: session.user.email },
			updateData,
			{ new: true }
		);

		console.log('User update API: Updated user:', {
			username: updatedUser.username,
			email: updatedUser.email,
			country: updatedUser.country || 'not set'
		});

		return NextResponse.json({ message: 'Profile updated successfully' });
	} catch (error) {
		console.error('Profile update error:', error);
		return NextResponse.json(
			{ message: 'An error occurred while updating the profile' },
			{ status: 500 }
		);
	}
}
