'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ProjectCard } from './ProjectCard';
import { IProject } from '@/models/Project';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Footer, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function FeaturedProjects() {
	const [projects, setProjects] = useState<IProject[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(false);
	const [fetchAttempted, setFetchAttempted] = useState(false);

	useEffect(() => {
		// Prevent multiple fetch attempts
		if (fetchAttempted) return;

		async function loadFeaturedProjects() {
			try {
				setLoading(true);
				setError(false);
				setFetchAttempted(true);

				// Add cache-busting parameter
				const timestamp = Date.now();
				const response = await fetch(`/api/public-projects?featured=true&t=${timestamp}`, {
					cache: 'no-store'
				});

				if (!response.ok) {
					throw new Error('Failed to fetch featured projects');
				}
				const data = await response.json();
				console.log('Featured projects loaded:', data.length);
				setProjects(data);
			} catch (err) {
				console.error('Error fetching featured projects:', err);
				setError(true);
			} finally {
				setLoading(false);
			}
		}

		loadFeaturedProjects();
	}, [fetchAttempted]);

	if (loading) {
		return (
			<div className="my-12">
				<h2 className="text-2xl font-semibold mb-6">Featured Projects</h2>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-items-center">
					{[1, 2, 3].map((i) => (
						<Card key={i} className="flex flex-col w-full max-w-sm relative animate-pulse">
							<CardHeader>
								<div className="h-6 bg-muted rounded w-3/4"></div>
							</CardHeader>
							<CardContent className="flex-grow">
								<div className="relative w-full h-48 mb-4 bg-muted rounded-md"></div>
								<div className="mt-2 mb-3">
									<div className="h-4 bg-muted rounded w-full mb-2"></div>
									<div className="h-4 bg-muted rounded w-5/6"></div>
								</div>
								<div className="h-6 bg-muted rounded w-1/4 mt-2"></div>
							</CardContent>
							<CardFooter>
								<div className="h-10 bg-muted rounded w-full"></div>
							</CardFooter>
						</Card>
					))}
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="text-center text-xl mt-8 text-red-500">
				Failed to load featured projects. Please try again later.
			</div>
		);
	}

	if (projects.length === 0) {
		return (
			<div className="my-12">
				<h2 className="text-2xl font-semibold mb-6">Featured Projects</h2>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-items-center">
					{[1, 2, 3].map((i) => (
						<Card key={i} className="flex flex-col w-full max-w-sm relative">
							<CardHeader>
								<CardTitle>No Featured Project</CardTitle>
							</CardHeader>
							<CardContent className="flex-grow">
								<div className="relative w-full h-48 mb-4 bg-muted flex items-center justify-center rounded-md">
									<span className="text-muted-foreground">No image available</span>
								</div>
								<div className="mt-2 mb-3 text-sm text-muted-foreground">
									No project description available. Create a featured project to display here.
								</div>
							</CardContent>
							<CardFooter>
								<Button asChild className="w-full" variant="outline">
									<Link href="/dashboard/projects">Add Project</Link>
								</Button>
							</CardFooter>
						</Card>
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="my-6">
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-items-center">
				{projects.map((project) => (
					<ProjectCard key={project._id.toString()} project={project} />
				))}
			</div>
		</div>
	);
}
