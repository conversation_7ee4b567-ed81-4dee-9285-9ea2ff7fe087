'use client';

import { Button } from '@/components/ui/button';
import Link from 'next/link';
import Image from 'next/image';
import { useEffect, useState } from 'react';
import { ArrowRight } from 'lucide-react';

export default function Hero() {
	const [isLoaded, setIsLoaded] = useState(false);

	useEffect(() => {
		const timer = setTimeout(() => {
			setIsLoaded(true);
		}, 100);
		return () => clearTimeout(timer);
	}, []);

	return (
		<div className="relative overflow-hidden bg-gradient-to-b from-background to-muted/30 rounded-xl shadow-md">
			<div className="absolute inset-0 opacity-10 bg-grid-pattern"></div>
			<div className="relative z-10 flex flex-col md:flex-row items-center justify-between px-6 py-16 md:py-24 max-w-7xl mx-auto">
				<div className="text-center md:text-left md:w-1/2 mb-10 md:mb-0">
					<h1
						className={`text-4xl md:text-5xl lg:text-6xl font-bold mb-4 tracking-tight transition-all duration-700 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}
					>
						Welcome to <span className="text-primary relative inline-block after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-full after:h-[3px] after:bg-primary after:transform after:scale-x-0 after:transition-transform after:duration-500 hover:after:scale-x-100">RealSoft Games</span>
					</h1>
					<p
						className={`text-xl md:text-2xl mb-8 text-muted-foreground max-w-xl transition-all duration-700 delay-100 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}
					>
						Innovative game development and exciting digital products for Unity and beyond
					</p>
					<div
						className={`flex flex-wrap gap-4 justify-center md:justify-start transition-all duration-700 delay-200 ${isLoaded ? 'translate-y-0 opacity-100' : 'translate-y-4 opacity-0'}`}
					>
						<Button asChild size="lg" className="rounded-full px-8 group hover:shadow-md transition-all duration-300 hover:-translate-y-1">
							<Link href="/products" className="flex items-center gap-2">
								Explore Products
								<ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
							</Link>
						</Button>
						<Button asChild variant="outline" size="lg" className="rounded-full px-8 group hover:shadow-md transition-all duration-300 hover:-translate-y-1">
							<Link href="/projects" className="flex items-center gap-2">
								View Projects
								<ArrowRight className="h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
							</Link>
						</Button>
					</div>
				</div>
				<div
					className={`relative w-full md:w-1/2 h-64 md:h-80 flex justify-center transition-all duration-1000 ${isLoaded ? 'translate-y-0 opacity-100 rotate-0' : 'translate-y-8 opacity-0 rotate-6'}`}
				>
					<div className="relative w-64 h-64 md:w-80 md:h-80 rounded-full bg-primary/10 flex items-center justify-center animate-pulse-slow">
						<div className="absolute inset-0 rounded-full bg-primary/5 animate-ping-slow opacity-70"></div>
						<div className="relative w-48 h-48 md:w-64 md:h-64 hover:scale-105 transition-transform duration-500">
							<Image
								src="/images/RSG 128x128_Holo.png"
								fill
								alt="RealSoft Games Logo"
								className="object-contain drop-shadow-lg"
								priority
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
