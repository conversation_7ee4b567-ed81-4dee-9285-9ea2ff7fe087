'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { IMedia } from '@/models/Media';
import WebGLPlayer from '@/components/WebGLPlayer';
import { Copy, ExternalLink, Gamepad2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { cn } from '@/lib/utils';

interface WebGLDemoProps {
  media: IMedia;
  demoUrl?: string;
  className?: string;
}

export default function WebGLDemo({ media, demoUrl, className }: WebGLDemoProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { toast } = useToast();

  // If this isn't a WebGL media item, don't render anything
  if (media.type !== 'webgl') {
    return null;
  }

  // Log the media object to help with debugging
  console.log('WebGL media object:', media);

  const handleCopyLink = () => {
    // Use the custom demo URL if provided, otherwise use the hosted WebGL URL
    const linkToCopy = demoUrl || window.location.href;

    navigator.clipboard.writeText(linkToCopy).then(() => {
      toast({
        title: "Link copied!",
        description: "The demo link has been copied to your clipboard.",
        duration: 3000,
      });
    }).catch(err => {
      console.error('Failed to copy link:', err);
      toast({
        title: "Failed to copy",
        description: "Please try again or copy the URL manually.",
        variant: "destructive",
        duration: 3000,
      });
    });
  };

  const handleExternalLink = () => {
    if (demoUrl) {
      window.open(demoUrl, '_blank');
    }
  };

  return (
    <Card className={cn("w-full overflow-hidden", className)}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Gamepad2 className="h-5 w-5" />
            <CardTitle>{media.title || 'WebGL Demo'}</CardTitle>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleCopyLink}
              title="Copy demo link"
            >
              <Copy className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Copy Link</span>
            </Button>
            {demoUrl && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleExternalLink}
                title="Open in new tab"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Open</span>
              </Button>
            )}
          </div>
        </div>
        {media.productName && (
          <CardDescription>
            {media.companyName && `${media.companyName} - `}
            {media.productName}
            {media.buildVersion && ` (v${media.buildVersion})`}
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="p-0">
        <div
          className={cn(
            "transition-all duration-300 ease-in-out",
            isExpanded ? "h-[80vh]" : `h-[${media.height || 600}px]`
          )}
          style={{
            height: isExpanded ? '80vh' : `${media.height || 600}px`,
            maxHeight: isExpanded ? '80vh' : `${media.height || 600}px`
          }}
        >
          <WebGLPlayer
            media={{
              ...media,
              type: 'webgl',
              status: 'success'
            }}
            autoLoad={false}
            showFullscreenButton={true}
            className="w-full h-full"
          />
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          {isExpanded ? 'Collapse' : 'Expand'}
        </Button>
        <p className="text-xs text-muted-foreground">
          {media.width || 960} x {media.height || 600}
        </p>
      </CardFooter>
    </Card>
  );
}
