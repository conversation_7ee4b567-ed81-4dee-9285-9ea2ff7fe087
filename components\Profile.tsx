'use client';

import { useState, useRef, useEffect, useMemo } from 'react';
import { useSession } from 'next-auth/react';
import { useAvatar } from '@/contexts/AvatarContext';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import SafeAvatar from './SafeAvatar';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue
} from '@/components/ui/select';
import countryList from 'react-select-country-list';
import ErrorBoundary from '@/components/error-boundary';

const profileSchema = z.object({
	username: z
		.string()
		.min(3, { message: 'Userna<PERSON> must be at least 3 characters.' }),
	email: z.string().email({ message: 'Please enter a valid email address.' }),
	dateOfBirth: z.string(),
	country: z.string().optional(),
	currentPassword: z.string().optional()
});

const passwordSchema = z
	.object({
		currentPassword: z
			.string()
			.min(6, 'Password must be at least 6 characters'),
		newPassword: z.string().min(6, 'Password must be at least 6 characters'),
		confirmPassword: z.string().min(6, 'Password must be at least 6 characters')
	})
	.refine((data) => data.newPassword === data.confirmPassword, {
		message: "Passwords don't match",
		path: ['confirmPassword']
	});

export function Profile() {
	const { data: session, update } = useSession();
	const { avatarUrl, updateAvatar, refreshAvatar } = useAvatar();
	const [isUploading, setIsUploading] = useState(false);
	const { toast } = useToast();
	const fileInputRef = useRef<HTMLInputElement>(null);
	const countries = useMemo(() => {
		// Initialize the country list
		const countryData = countryList().getData();

		// Log a few countries to see their format
		console.log('Profile: Available countries sample:', countryData.slice(0, 5));

		// Return the country data without adding an empty option
		// The placeholder will be handled by the Select component
		return countryData;
	}, []);
	const [requirePassword, setRequirePassword] = useState(false);

	const profileForm = useForm<z.infer<typeof profileSchema>>({
		resolver: zodResolver(profileSchema),
		defaultValues: {
			username: '',
			email: '',
			dateOfBirth: '',
			country: '',
			currentPassword: ''
		}
	});

	const passwordForm = useForm<z.infer<typeof passwordSchema>>({
		resolver: zodResolver(passwordSchema),
		defaultValues: {
			currentPassword: '',
			newPassword: '',
			confirmPassword: ''
		}
	});

	useEffect(() => {
		if (session?.user) {
			console.log('Profile: Session user data:', {
				username: session.user.username,
				email: session.user.email,
				dateOfBirth: session.user.dateOfBirth,
				country: session.user.country,
				avatar: session.user.avatar ? 'exists' : 'missing'
			});

			// Check if the country exists in the country list
			if (session.user.country) {
				// Find the country in the list
				const countryExists = countries.some(c => c.value === session.user.country);
				console.log(`Profile: Country ${session.user.country} exists in country list: ${countryExists}`);

				// If the country doesn't exist in the list, try to find it by label
				if (!countryExists) {
					const countryByLabel = countries.find(c => c.label === session.user.country);
					if (countryByLabel) {
						console.log(`Profile: Found country by label: ${countryByLabel.value}`);
					}
				}
			}

			// Determine the correct country value
			let countryValue = session.user.country || ''; // Start with the session value

			// Log all countries for debugging
			console.log('Profile: All countries:', countries.map(c => ({ value: c.value, label: c.label })));

			if (session.user.country) {
				// First check if the country code exists directly
				const countryExists = countries.some(c => c.value === session.user.country);
				console.log(`Profile: Country ${session.user.country} exists in country list: ${countryExists}`);

				if (countryExists) {
					countryValue = session.user.country;
					console.log(`Profile: Using country value directly: ${countryValue}`);
				} else {
					// If not, try to find it by label
					const countryByLabel = countries.find(c => c.label === session.user.country);
					if (countryByLabel) {
						countryValue = countryByLabel.value;
						console.log(`Profile: Found country by label: ${countryByLabel.value}`);
					} else {
						// Try to find a country that contains the session country value
						const countryMatch = countries.find(c =>
							c.value.toLowerCase() === session.user.country?.toLowerCase() ||
							c.label.toLowerCase().includes(session.user.country?.toLowerCase() || ''));

						if (countryMatch) {
							countryValue = countryMatch.value;
							console.log(`Profile: Found country by partial match: ${countryMatch.value} (${countryMatch.label})`);
						}
					}
				}
			}

			console.log(`Profile: Setting country value to: ${countryValue || 'empty string'}`);

			// Force the form to use the country value we determined
			profileForm.setValue('country', countryValue);

			// Reset the form with all values
			profileForm.reset({
				username: session.user.username || '',
				email: session.user.email || '',
				dateOfBirth: session.user.dateOfBirth || '',
				country: countryValue
			});

			console.log('Profile: Form values after reset:', profileForm.getValues());
		}
	}, [session, profileForm]);

	const handleAvatarUpload = async (
		event: React.ChangeEvent<HTMLInputElement>
	) => {
		const file = event.target.files?.[0];
		if (!file) return;

		setIsUploading(true);
		const formData = new FormData();
		formData.append('file', file);

		try {
			// console.log('Profile: Starting avatar upload');
			// Step 1: Upload the file
			const uploadResponse = await fetch('/api/upload?type=avatar', {
				method: 'POST',
				body: formData
			});

			if (!uploadResponse.ok) {
				throw new Error('Failed to upload avatar');
			}

			const uploadData = await uploadResponse.json();
			// console.log('Profile: File uploaded successfully, URL:', uploadData.url);

			// Step 2: Update the user's avatar in the database
			const updateResponse = await fetch('/api/user/update-avatar', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ avatarUrl: uploadData.url })
			});

			if (!updateResponse.ok) {
				throw new Error('Failed to update user avatar');
			}

			// Get the response data
			const updateData = await updateResponse.json();
			// console.log('Profile: Avatar updated in database:', updateData);

			// Step 3: Update the avatar in the context
			// console.log('Profile: Updating avatar in context with:', updateData.avatarUrl);
			updateAvatar(updateData.avatarUrl);

			// Step 4: Force refresh the avatar display
			// console.log('Profile: Refreshing avatar display');
			refreshAvatar();

			// Step 5: Update the local session state and force a refresh
			if (session && session.user) {
				// console.log('Profile: Updating session with new avatar URL');
				session.user.avatar = updateData.avatarUrl;
				try {
					await update({
						...session,
						user: {
							...session.user,
							avatar: updateData.avatarUrl
						}
					});
					// console.log('Profile: Session updated successfully');
				} catch (error) {
					console.error('Profile: Error updating session:', error);
					// Continue anyway - the avatar context should still work
				}
			}

			toast({
				title: 'Success',
				description: 'Avatar uploaded and updated successfully',
				type: 'success'
			});
		} catch (error) {
			console.error('Error uploading avatar:', error);
			toast({
				title: 'Error',
				description: 'Failed to upload or update avatar',
				type: 'error'
			});
		} finally {
			setIsUploading(false);
		}
	};

	const onProfileSubmit = async (values: z.infer<typeof profileSchema>) => {
		console.log('Profile: Submitting form with values:', values);

		if (requirePassword && !values.currentPassword) {
			toast({
				title: 'Error',
				description:
					'Please enter your current password to update email or username.'
			});
			profileForm.setError('currentPassword', {
				type: 'manual',
				message: 'Current password is required to update email or username.'
			});
			return;
		}

		try {
			// Make sure country is properly set
			const dataToSend = {
				...values,
				country: values.country || null // Ensure country is explicitly set to null if empty
			};

			console.log('Profile: Sending data to API:', dataToSend);

			const response = await fetch('/api/user/update', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(dataToSend)
			});

			if (response.ok) {
				toast({
					title: 'Success',
					description: 'Profile updated successfully.'
				});
				// Update the session with the new values
				try {
					// Create a clean session update object with only the fields we want to update
					const sessionUpdateData = {
						username: dataToSend.username,
						email: dataToSend.email,
						dateOfBirth: dataToSend.dateOfBirth,
						country: dataToSend.country
					};

					console.log('Profile: Updating session with:', sessionUpdateData);

					// Skip the session update and just reload the page
					// This will force a new session to be fetched with the updated data
					console.log('Profile: Skipping session update, reloading page instead');
					window.location.reload();
				} catch (error) {
					console.error('Profile: Error updating session after profile update:', error);
				}
				setRequirePassword(false);
			} else {
				const data = await response.json();
				throw new Error(data.message || 'Failed to update profile');
			}
		} catch (error) {
			toast({
				title: 'Error',
				description:
					error instanceof Error
						? error.message
						: 'An error occurred while updating your profile.'
			});
		}
	};

	const onPasswordSubmit = async (values: z.infer<typeof passwordSchema>) => {
		try {
			const response = await fetch('/api/user/update-password', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify(values)
			});

			if (response.ok) {
				toast({
					title: 'Success',
					description: 'Password updated successfully',
					type: 'success'
				});
				passwordForm.reset();
			} else {
				throw new Error('Failed to update password');
			}
		} catch (error) {
			toast({
				title: 'Error',
				description: 'An error occurred while updating your password.',
				type: 'error'
			});
		}
	};

	useEffect(() => {
		const subscription = profileForm.watch((_, { name }) => {
			if (name === 'email' || name === 'username') {
				setRequirePassword(true);
			}
		});
		return () => subscription.unsubscribe();
	}, [profileForm]);

	if (!session?.user) return null;

	return (
		<ErrorBoundary componentName="Profile">
			<div className="space-y-6">
				<Card>
					<CardHeader>
						<CardTitle>Profile</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						<div className="flex flex-col items-center space-y-4">
							{/* Use SafeAvatar component which works correctly */}
							<SafeAvatar
								src={session.user.avatar}
								alt={session.user.username}
								fallback={session.user.username.charAt(0).toUpperCase()}
								className="w-24 h-24"
							/>
							<input
								type="file"
								accept="image/*"
								onChange={handleAvatarUpload}
								disabled={isUploading}
								className="hidden"
								ref={fileInputRef}
							/>
							<Button
								onClick={() => fileInputRef.current?.click()}
								disabled={isUploading}
							>
								{isUploading ? 'Uploading...' : 'Change Avatar'}
							</Button>
						</div>

						<Form {...profileForm}>
							<form
								onSubmit={profileForm.handleSubmit(onProfileSubmit)}
								className="space-y-4"
							>
								<FormField
									control={profileForm.control}
									name="username"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Username</FormLabel>
											<FormControl>
												<Input {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={profileForm.control}
									name="email"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Email</FormLabel>
											<FormControl>
												<Input {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={profileForm.control}
									name="dateOfBirth"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Date of Birth</FormLabel>
											<FormControl>
												<Input type="date" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={profileForm.control}
									name="country"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Country</FormLabel>
											<Select
												onValueChange={(value) => {
													field.onChange(value);
												}}
												value={field.value || session.user.country || ''}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select a country" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{countries.map((country) => (
														<SelectItem
															key={country.value}
															value={country.value}
														>
															{country.label}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
								{requirePassword && (
									<FormField
										control={profileForm.control}
										name="currentPassword"
										render={({ field }) => (
											<FormItem>
												<FormLabel>Current Password</FormLabel>
												<FormControl>
													<Input type="password" autoComplete="current-password" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								)}
								<Button type="submit">Update Profile</Button>
							</form>
						</Form>
					</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Change Password</CardTitle>
					</CardHeader>
					<CardContent>
						<Form {...passwordForm}>
							<form
								onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
								className="space-y-4"
							>
								<FormField
									control={passwordForm.control}
									name="currentPassword"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Current Password</FormLabel>
											<FormControl>
												<Input type="password" autoComplete="current-password" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={passwordForm.control}
									name="newPassword"
									render={({ field }) => (
										<FormItem>
											<FormLabel>New Password</FormLabel>
											<FormControl>
												<Input type="password" autoComplete="new-password" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={passwordForm.control}
									name="confirmPassword"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Confirm New Password</FormLabel>
											<FormControl>
												<Input type="password" autoComplete="new-password" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<Button type="submit">Update Password</Button>
							</form>
						</Form>
					</CardContent>
				</Card>
			</div>
		</ErrorBoundary>
	);
}
