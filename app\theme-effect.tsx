'use client';

import { useTheme } from 'next-themes';
import { useEffect } from 'react';

// This component applies the theme after React hydration
export default function ThemeEffect() {
  const { setTheme, resolvedTheme } = useTheme();

  useEffect(() => {
    // Set the theme to dark by default if not already set
    if (!resolvedTheme || resolvedTheme === 'system') {
      setTheme('dark');
    }
  }, [resolvedTheme, setTheme]);

  return null;
}
