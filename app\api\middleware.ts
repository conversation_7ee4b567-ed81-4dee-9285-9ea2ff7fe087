import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { connectToDatabase } from '@/lib/mongoose';
import ApiToken from '@/models/ApiToken';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { cookies, headers } from 'next/headers';

/**
 * Middleware to validate and track API token usage
 */
export async function validateApiToken(req: Request | NextRequest) {
  // Get the API token from the Authorization header
  const authHeader = req.headers.get('Authorization');
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }

  const token = authHeader.substring(7); // Remove 'Bearer ' prefix

  try {
    await connectToDatabase();

    // Find the token in the database
    const apiToken = await ApiToken.findOne({ token });

    if (!apiToken) {
      console.log('API token not found:', token.substring(0, 10) + '...');
      return null;
    }

    // Update token usage statistics using the model method
    await apiToken.incrementUsage();

    return apiToken;
  } catch (error) {
    console.error('Error validating API token:', error);
    return null;
  }
}

/**
 * Middleware to protect API routes with token authentication or session authentication
 */
export async function withApiAuth(req: Request | NextRequest, handler: (req: Request | NextRequest, apiToken?: any) => Promise<Response>) {
  // First, try to validate the API token
  const apiToken = await validateApiToken(req);

  if (apiToken) {
    // If we have a valid API token, proceed with the handler
    return handler(req, apiToken);
  }

  // If no API token, check for a valid session
  try {
    // Always await cookies and headers directly at the top level
    // This ensures they're properly awaited before any other operations
    const cookiesList = await cookies();
    const headersList = await headers();

    // Get the session with the properly awaited cookies and headers
    const options = await authOptions();

    // Create a new headers object with the cookies from cookiesList
    const headerObj = new Headers();
    for (const cookie of cookiesList.getAll()) {
      headerObj.append('cookie', `${cookie.name}=${cookie.value}`);
    }

    // Pass the headers to getServerSession
    const session = await getServerSession(options);

    if (session && session.user) {
      // User is authenticated via session, allow access
      return handler(req, null);
    }

    // No valid API token or session
    return NextResponse.json(
      { error: 'Unauthorized. Invalid or missing API token or session.' },
      { status: 401 }
    );
  } catch (error) {
    console.error('Error checking session:', error);
    return NextResponse.json(
      { error: 'Unauthorized. Authentication error.' },
      { status: 401 }
    );
  }
}
