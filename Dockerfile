# Build stage
FROM node:23-slim AS builder
WORKDIR /app

# Install dependencies required for Sharp
RUN apt-get update && apt-get install -y --no-install-recommends \
    libvips-dev \
    build-essential \
    python3 \
    && rm -rf /var/lib/apt/lists/*

# Set npm to production mode for better security
ENV NODE_ENV=production

# Copy package files
COPY package.json package-lock.json ./

# Install all dependencies including dev dependencies
RUN npm ci --legacy-peer-deps

# Copy the rest of the application
COPY . .

# Copy .env.example to .env for build
COPY .env.example .env

# Build the application
RUN npm run build

# Production stage
FROM node:23-slim AS runner
WORKDIR /app

# Install dependencies required for Sharp
RUN apt-get update && apt-get install -y --no-install-recommends \
    libvips-dev \
    && rm -rf /var/lib/apt/lists/*

# Use non-root user for better security
RUN addgroup --system --gid 1001 nodejs \
    && adduser --system --uid 1001 nextjs

# Create public directory
RUN mkdir -p public

# Copy the standalone build from the builder stage
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/public ./public

# Create scripts directory if it doesn't exist
RUN mkdir -p scripts

# Set correct permissions
RUN chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Expose the port the app will run on
EXPOSE 3000

# Set environment variables
ENV NODE_ENV=production
ENV PORT=3000

# Start the Next.js server with proper signal handling
CMD ["node", "server.js"]
