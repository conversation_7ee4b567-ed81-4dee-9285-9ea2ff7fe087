import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export const runtime = 'nodejs';

export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const url = searchParams.get('url');

    if (!url) {
      return NextResponse.json({ error: 'No URL provided' }, { status: 400 });
    }

    // Decode the URL if it's encoded
    const decodedUrl = decodeURIComponent(url);
    const timestamp = searchParams.get('t'); // Get timestamp for cache busting

    // Use the decoded URL directly - minimize processing for speed
    let processedUrl = decodedUrl;

    // Only do minimal processing for MinIO URLs if needed
    if (decodedUrl.includes('minioapi.realsoftgames.com') || decodedUrl.includes('192.168.0.59')) {
      // Just ensure the URL is properly formed, but don't do extensive processing
      if (!decodedUrl.startsWith('http')) {
        processedUrl = `https://${decodedUrl}`;
      }
    }

    // Add timestamp to URL if provided (but keep it simple)
    let fetchUrl = processedUrl;
    if (timestamp && !processedUrl.includes('?')) {
      fetchUrl = `${processedUrl}?t=${timestamp}`;
    } else if (timestamp && !processedUrl.includes('t=')) {
      fetchUrl = `${processedUrl}&t=${timestamp}`;
    }

    // Fetch the image from MinIO with optimized settings
    const response = await fetch(fetchUrl, {
      headers: {
        'Accept': 'image/*',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Cache-Control': 'max-age=31536000' // Request caching for 1 year
      },
      // Use only-if-cached first, then fall back to force-cache for better performance
      cache: 'force-cache',
      next: { revalidate: 604800 } // Revalidate once per week for better performance
    });

    if (!response.ok) {
      return NextResponse.json(
        { error: `Failed to fetch image: ${response.statusText}` },
        { status: response.status }
      );
    }

    // Get the image data as an array buffer
    const imageData = await response.arrayBuffer();

    // Create a new response with the image data
    const imageResponse = new NextResponse(imageData);

    // Copy the content type and other relevant headers
    imageResponse.headers.set('Content-Type', response.headers.get('Content-Type') || 'application/octet-stream');
    imageResponse.headers.set('Content-Length', response.headers.get('Content-Length') || String(imageData.byteLength));

    // Set aggressive caching headers for better performance
    imageResponse.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
    imageResponse.headers.set('Access-Control-Allow-Origin', '*');
    imageResponse.headers.set('X-Cache-Optimized', 'true');

    return imageResponse;
  } catch (error) {
    console.error('Error proxying image:', error);
    return NextResponse.json(
      { error: 'Failed to proxy image' },
      { status: 500 }
    );
  }
}
