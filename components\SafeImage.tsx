'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';

interface SafeImageProps {
  src: string | null | undefined;
  fallbackSrc?: string;
  alt?: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
  quality?: number;
  sizes?: string;
  style?: React.CSSProperties;
  fill?: boolean;
}

export default function SafeImage({
  src,
  fallbackSrc = '',
  alt,
  className,
  ...props
}: SafeImageProps) {
  const [error, setError] = useState(false);
  const [imageSrc, setImageSrc] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);

  // Process the source URL on the client side only
  useEffect(() => {
    setIsLoading(true);
    setError(false);

    if (!src || src === '') {
      if (fallbackSrc) {
        setImageSrc(fallbackSrc);
      } else {
        setError(true);
      }
      setIsLoading(false);
      return;
    }

    if (typeof src === 'string') {
      // Use direct URLs whenever possible for better performance
      if (src.includes('minioapi.realsoftgames.com')) {
        // Use the direct URL without any cache busting for better caching
        setImageSrc(src);
      } else if (src.includes('192.168.0.59')) {
        // For local network URLs, convert to public URLs for external access
        const publicUrl = src.replace('http://192.168.0.59:9000', 'https://minioapi.realsoftgames.com');
        setImageSrc(publicUrl);
      } else if (src.startsWith('/minio/')) {
        // For /minio/ paths, convert to direct MinIO URLs
        const pathParts = src.split('/');
        if (pathParts.length >= 3) {
          const bucket = pathParts[2];
          const key = pathParts.slice(3).join('/');
          setImageSrc(`https://minioapi.realsoftgames.com/${bucket}/${key}`);
        } else {
          setImageSrc(src);
        }
      } else {
        // For non-MinIO images, use the original URL
        setImageSrc(src);
      }
    } else {
      if (fallbackSrc) {
        setImageSrc(fallbackSrc);
      } else {
        setError(true);
      }
      setIsLoading(false);
    }
  }, [src, fallbackSrc]);

  const handleError = () => {
    console.error(`Failed to load image: ${src}`);
    setError(true);
    setIsLoading(false);

    if (fallbackSrc) {
      // Use the provided fallback image
      setImageSrc(fallbackSrc);
    } else if (typeof src === 'string' && src.includes('minioapi.realsoftgames.com')) {
      // If direct MinIO URL fails, try the optimized image API as a last resort
      try {
        // Extract bucket and key from the URL
        const urlObj = new URL(src);
        const pathParts = urlObj.pathname.split('/');

        if (pathParts.length >= 2) {
          const bucket = pathParts[1];
          const key = pathParts.slice(2).join('/');

          // Use the optimized image API as a fallback
          const optimizedUrl = `/api/optimized-image?bucket=${encodeURIComponent(bucket)}&key=${encodeURIComponent(key)}`;
          setImageSrc(optimizedUrl);
          return; // Don't set error yet, give the optimized API a chance
        }
      } catch (err) {
        // If parsing fails, continue to error state
        console.error('Error parsing MinIO URL:', err);
      }

      // If we get here, we couldn't extract bucket/key or there was an error
      setError(true);
      setIsLoading(false);
    } else {
      // For non-MinIO images, just show the error state
      setError(true);
      setIsLoading(false);
    }
  };

  // If no valid source is available, render a placeholder
  if (!imageSrc && !fallbackSrc) {
    return (
      <div className={`bg-gray-200 dark:bg-gray-700 animate-pulse rounded ${className || ''}`} style={props.style}>
        <div className="flex items-center justify-center h-full w-full">
          <span className="text-gray-400 dark:text-gray-500">No Image</span>
        </div>
      </div>
    );
  }

  // If no valid source is available and no fallback, render a placeholder
  if ((!imageSrc || error) && !fallbackSrc) {
    return (
      <div className={`bg-gray-200 dark:bg-gray-700 rounded ${className || ''}`}
           style={props.fill ? { width: '100%', height: '100%', ...props.style } : { width: props.width || 500, height: props.height || 300, ...props.style }}>
        <div className="flex items-center justify-center h-full w-full">
          <span className="text-gray-400 dark:text-gray-500">No Image</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className || ''}`} style={props.fill ? { width: '100%', height: '100%', ...props.style } : props.style}>
      {imageSrc && (
        <Image
          {...props}
          className={`${isLoading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-300 ${props.className || ''}`}
          src={error && fallbackSrc ? fallbackSrc : imageSrc}
          alt={alt || 'Image'}
          onError={handleError}
          onLoad={() => setIsLoading(false)}
          placeholder="blur"
          blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEDQIHXG8ZQQAAAABJRU5ErkJggg=="
          width={props.fill ? undefined : (props.width || 500)}
          height={props.fill ? undefined : (props.height || 300)}
        />
      )}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-200 dark:bg-gray-700 animate-pulse rounded">
          <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        </div>
      )}
    </div>
  );
}
