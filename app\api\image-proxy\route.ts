import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { withCors } from '@/app/api/cors-middleware';

export const runtime = 'nodejs';

// We use a static buffer for the fallback image instead of a URL

async function handler(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const url = searchParams.get('url');
    const timestamp = searchParams.get('t'); // Get timestamp for cache busting

    if (!url) {
      return NextResponse.json({ error: 'No URL provided' }, { status: 400 });
    }

    // Decode the URL if it's encoded
    const decodedUrl = decodeURIComponent(url);

    // Use the decoded URL directly - minimize processing for speed
    let processedUrl = decodedUrl;

    // Only do minimal processing for MinIO URLs if needed
    if (decodedUrl.includes('minioapi.realsoftgames.com') || decodedUrl.includes('192.168.0.59')) {
      // Just ensure the URL is properly formed, but don't do extensive processing
      if (!decodedUrl.startsWith('http')) {
        processedUrl = `https://${decodedUrl}`;
      }
    }

    // Fetch the image from the source with minimal retry logic
    let response;
    let retries = 2; // Reduced retries for faster response

    while (retries > 0) {
      try {
        // Add timestamp to URL if not already present and URL doesn't already have query params
        let fetchUrl = processedUrl;
        if (timestamp && !processedUrl.includes('?')) {
          fetchUrl = `${processedUrl}?t=${timestamp}`;
        } else if (timestamp && !processedUrl.includes('t=')) {
          fetchUrl = `${processedUrl}&t=${timestamp}`;
        }

        response = await fetch(fetchUrl, {
          headers: {
            'Accept': 'image/*',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Cache-Control': 'max-age=31536000' // Request caching for 1 year
          },
          // Use force-cache for better performance
          cache: 'force-cache',
          next: { revalidate: 604800 } // Revalidate once per week for better performance
        });

        if (response.ok) break;

        console.warn(`Retry ${3 - retries}/2: Failed to fetch image: ${response.status} ${response.statusText}`);
        retries--;

        if (retries > 0) {
          // Wait before retrying (shorter backoff)
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      } catch (error) {
        console.error(`Retry ${3 - retries}/2: Error fetching image:`, error);
        retries--;

        if (retries > 0) {
          // Wait before retrying (shorter backoff)
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }
    }

    if (!response || !response.ok) {
      console.error(`Failed to fetch image after retries: ${response?.statusText || 'Network error'}`);

      // Return a transparent 1x1 pixel instead of an error
      // Use a static buffer instead of fetching the fallback image
      const fallbackData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEDQIHXG8ZQQAAAABJRU5ErkJggg==', 'base64');

      const imageResponse = new NextResponse(fallbackData);
      imageResponse.headers.set('Content-Type', 'image/png');
      imageResponse.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
      return imageResponse;
    }

    try {
      // Get the image data as an array buffer
      const imageData = await response.arrayBuffer();

      // Create a new response with the image data
      const imageResponse = new NextResponse(imageData);

      // Copy the content type and other relevant headers
      const contentType = response.headers.get('Content-Type');
      imageResponse.headers.set('Content-Type', contentType || 'image/jpeg');
      imageResponse.headers.set('Content-Length', response.headers.get('Content-Length') || String(imageData.byteLength));

      // Set aggressive caching headers for better performance
      imageResponse.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
      imageResponse.headers.set('Access-Control-Allow-Origin', '*');
      imageResponse.headers.set('X-Cache-Optimized', 'true');

      return imageResponse;
    } catch (imageError) {
      console.error('Error processing image data:', imageError);

      // Return a transparent 1x1 pixel as fallback
      // Use a static buffer instead of fetching the fallback image
      const fallbackData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEDQIHXG8ZQQAAAABJRU5ErkJggg==', 'base64');

      const imageResponse = new NextResponse(fallbackData);
      imageResponse.headers.set('Content-Type', 'image/png');
      imageResponse.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
      return imageResponse;
    }
  } catch (error) {
    console.error('Error proxying image:', error);

    // Return a transparent 1x1 pixel instead of an error JSON
    try {
      // Use a static buffer instead of fetching the fallback image
      const fallbackData = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEDQIHXG8ZQQAAAABJRU5ErkJggg==', 'base64');

      const imageResponse = new NextResponse(fallbackData);
      imageResponse.headers.set('Content-Type', 'image/png');
      imageResponse.headers.set('Cache-Control', 'public, max-age=31536000, immutable');
      return imageResponse;
    } catch (fallbackError) {
      // If even the fallback fails, return a JSON error
      return NextResponse.json(
        { error: 'Failed to proxy image' },
        { status: 500 }
      );
    }
  }
}

// Apply CORS middleware to the handler
export const GET = (req: NextRequest) => withCors(req, handler);

// Handle OPTIONS requests for CORS
export const OPTIONS = (req: NextRequest) => withCors(req, async () => new NextResponse(null, { status: 204 }));
