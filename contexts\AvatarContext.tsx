'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

type AvatarContextType = {
  avatarUrl: string | undefined;
  updateAvatar: (newUrl: string) => void;
  refreshAvatar: () => void;
};

const AvatarContext = createContext<AvatarContextType | undefined>(undefined);

export function AvatarProvider({ children }: { children: React.ReactNode }) {
  const { data: session, update } = useSession();
  const [avatarKey, setAvatarKey] = useState(Date.now());
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(undefined);

  // Initialize avatar URL from session
  useEffect(() => {
    try {
      if (session?.user?.avatar) {
        // console.log('AvatarContext: Session avatar URL:', session.user.avatar);

        // Use the direct URL if it's a MinIO URL
        if (session.user.avatar.includes('minioapi.realsoftgames.com')) {
          const separator = session.user.avatar.includes('?') ? '&' : '?';
          const url = `${session.user.avatar}${separator}t=${avatarKey}`;
          // console.log('AvatarContext: Setting direct MinIO URL:', url);
          setAvatarUrl(url);
        } else {
          // Otherwise use the proxy
          const url = `/api/avatar-proxy?url=${encodeURIComponent(session.user.avatar)}&t=${avatarKey}`;
          // console.log('AvatarContext: Setting proxy URL:', url);
          setAvatarUrl(url);
        }
      } else {
        // console.log('AvatarContext: No avatar in session');
        setAvatarUrl(undefined);
      }
    } catch (error) {
      console.error('AvatarContext: Error setting avatar URL:', error);
      setAvatarUrl(undefined);
    }
  }, [session?.user?.avatar, avatarKey]);

  // Preload the avatar image to check if it's valid
  useEffect(() => {
    if (avatarUrl) {
      const img = new Image();
      img.onload = () => {
        // console.log('AvatarContext: Avatar image loaded successfully');
      };
      img.onerror = () => {
        // console.error('AvatarContext: Avatar image failed to load');
        // Don't set to undefined here - let the Avatar component handle the error
      };
      img.src = avatarUrl;
    }
  }, [avatarUrl]);

  // Function to update avatar in context
  const updateAvatar = (newUrl: string) => {
    try {
      // console.log('AvatarContext: Updating avatar with new URL:', newUrl);
      // Set the avatar URL directly
      const timestamp = Date.now();

      // Use the direct URL if it's a MinIO URL
      if (newUrl.includes('minioapi.realsoftgames.com')) {
        const separator = newUrl.includes('?') ? '&' : '?';
        const url = `${newUrl}${separator}t=${timestamp}`;
        // console.log('AvatarContext: Setting direct MinIO URL:', url);
        setAvatarUrl(url);

        // Also update the session
        if (session && session.user) {
          session.user.avatar = newUrl;
          update().catch(error => {
            console.error('AvatarContext: Error updating session:', error);
          });
        }
      } else {
        // Otherwise use the proxy
        const proxyUrl = `/api/avatar-proxy?url=${encodeURIComponent(newUrl)}&t=${timestamp}`;
        // console.log('AvatarContext: Setting proxy URL:', proxyUrl);
        setAvatarUrl(proxyUrl);

        // Also update the session
        if (session && session.user) {
          session.user.avatar = newUrl;
          update().catch(error => {
            console.error('AvatarContext: Error updating session:', error);
          });
        }
      }

      // Force refresh by updating the key
      setAvatarKey(timestamp);

      // Dispatch a custom event that other components can listen for
      const avatarUpdateEvent = new CustomEvent('avatar-updated', {
        detail: { avatarUrl: newUrl, timestamp: timestamp }
      });
      window.dispatchEvent(avatarUpdateEvent);
    } catch (error) {
      console.error('Error updating avatar in context:', error);
    }
  };

  // Function to refresh avatar (force re-render)
  const refreshAvatar = () => {
    try {
      // console.log('AvatarContext: Refreshing avatar');
      const newKey = Date.now();
      setAvatarKey(newKey);

      if (session?.user?.avatar) {
        // console.log('AvatarContext: Refresh with avatar URL:', session.user.avatar);

        // Use the direct URL if it's a MinIO URL
        if (session.user.avatar.includes('minioapi.realsoftgames.com')) {
          const separator = session.user.avatar.includes('?') ? '&' : '?';
          const url = `${session.user.avatar}${separator}t=${newKey}`;
          // console.log('AvatarContext: Refresh with direct MinIO URL:', url);
          setAvatarUrl(url);
        } else {
          // Otherwise use the proxy
          const url = `/api/avatar-proxy?url=${encodeURIComponent(session.user.avatar)}&t=${newKey}`;
          // console.log('AvatarContext: Refresh with proxy URL:', url);
          setAvatarUrl(url);
        }
      } else {
        // console.log('AvatarContext: No avatar to refresh');
      }

      // Also update the session to ensure it has the latest avatar URL
      if (session) {
        update().catch(error => {
          console.error('AvatarContext: Error updating session:', error);
        });
      }
    } catch (error) {
      console.error('AvatarContext: Error refreshing avatar:', error);
    }
  };

  return (
    <AvatarContext.Provider value={{ avatarUrl, updateAvatar, refreshAvatar }}>
      {children}
    </AvatarContext.Provider>
  );
}

export function useAvatar() {
  const context = useContext(AvatarContext);
  if (context === undefined) {
    throw new Error('useAvatar must be used within an AvatarProvider');
  }
  return context;
}
