export const dynamic = 'force-dynamic';

import { NextResponse } from 'next/server';
import { connectToDatabase } from '@/lib/mongoose';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import User from '@/models/User';
import Project from '@/models/Project';
import Product from '@/models/Product';

export async function GET(request: Request) {
	try {
		const session = await getServerSession(await authOptions());
		if (!session) {
			return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
		}

		await connectToDatabase();

		const totalUsers = await User.countDocuments();
		const totalProjects = await Project.countDocuments();
		const totalProducts = await Product.countDocuments();

		const countryData = await User.aggregate([
			{
				$group: {
					_id: '$country',
					count: { $sum: 1 }
				}
			},
			{
				$project: {
					name: '$_id',
					value: '$count'
				}
			},
			{
				$sort: { value: -1 }
			}
		]);

		const usersWithoutCountry = await User.countDocuments({
			country: { $in: [null, ''] }
		});

		return NextResponse.json({
			totalUsers,
			totalProjects,
			totalProducts,
			countryData,
			usersWithoutCountry
		});
	} catch (error) {
		console.error('Error fetching analytics:', error);
		return NextResponse.json(
			{
				message: 'An error occurred while fetching analytics',
				error: error instanceof Error ? error.message : String(error)
			},
			{ status: 500 }
		);
	}
}
