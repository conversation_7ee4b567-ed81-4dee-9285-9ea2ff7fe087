'use client'

import { useSession } from 'next-auth/react';

export function SessionInfo() {
  const { data: session, status } = useSession();

  if (status === 'loading') {
    return <div>Loading session...</div>;
  }

  if (status === 'authenticated') {
    return (
      <div>
        <h2>Authenticated</h2>
        <p>Signed in as: {session.user?.email}</p>
        <pre>{JSON.stringify(session, null, 2)}</pre>
      </div>
    );
  }

  return <div>Not authenticated</div>;
}