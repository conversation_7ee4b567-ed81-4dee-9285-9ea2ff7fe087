'use client';

import { useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { CheckCircle } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

export default function ContactConfirmationPage() {
  const searchParams = useSearchParams();
  const name = searchParams?.get('name') || '';

  // Scroll to top on page load
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="container mx-auto px-4 py-16 flex flex-col items-center text-center">
      <div className="w-full max-w-md flex flex-col items-center">
        <CheckCircle className="h-16 w-16 text-green-500 mb-6" />
        <h1 className="text-3xl font-bold mb-4">Thank You{name ? `, ${name}` : ''}!</h1>
        <p className="text-lg mb-8">
          Your message has been sent successfully. We'll get back to you as soon as possible.
        </p>
        <div className="flex flex-col sm:flex-row gap-4">
          <Link href="/">
            <Button variant="outline">Return to Home</Button>
          </Link>
          <Link href="/contact">
            <Button>Send Another Message</Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
